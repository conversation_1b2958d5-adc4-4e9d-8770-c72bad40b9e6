2025-06-23 23:35:36 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:35:36 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.15,
  "convertedSlippage": "0.15 (15.00%)",
  "inputPriorityFee": 0.0003,
  "convertedPriorityFee": "3000000 microLamports",
  "inputBribeAmount": 0.0001,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:35:36 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "0.00001 SOL",
  "slippage": "15.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:35:36 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "slippageValue": 0.15,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-23 23:35:36 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:35:36 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "buy",
  "amount": 0.00001,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:35:36 info: Processing BUY transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:35:36 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "36873055511",
  "virtualTokenReserves": "872995187676375"
}
2025-06-23 23:35:36 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.00001 SOL",
  "output": "234.389311 tokens",
  "price": "0.000000043 SOL per token"
}
2025-06-23 23:35:37 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 5,
  "operationType": "buy",
  "feePayer": "********************************************",
  "recentBlockhash": "GWMS8ccw7Kf2AKf4uU1S1LXhJTcumo6h5FhwLp68xb2e"
}
2025-06-23 23:35:37 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:35:37 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:35:37 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-23 23:35:41 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "3xPo88y7us1aRBXCZ1B1KDXwDRfredtKyqHFFx33szRfyM8jUgfp2EwY3xHNTYXm8hbsH7o32EApRW1T4dYCSyaz"
}
2025-06-23 23:35:41 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "3xPo88y7us1aRBXCZ1B1KDXwDRfredtKyqHFFx33szRfyM8jUgfp2EwY3xHNTYXm8hbsH7o32EApRW1T4dYCSyaz",
  "outputAmount": "234.389311 tokens"
}
2025-06-23 23:36:00 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:36:00 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.15,
  "convertedSlippage": "0.15 (15.00%)",
  "inputPriorityFee": 0.0003,
  "convertedPriorityFee": "3000000 microLamports",
  "inputBribeAmount": 0.0001,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:36:00 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "0.00001 SOL",
  "slippage": "15.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:36:00 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "slippageValue": 0.15,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-23 23:36:00 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:36:00 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "buy",
  "amount": 0.00001,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-23 23:36:00 info: Processing BUY transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:36:01 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "39799180061",
  "virtualTokenReserves": "808810633058204"
}
2025-06-23 23:36:01 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.00001 SOL",
  "output": "201.19066 tokens",
  "price": "0.000000050 SOL per token"
}
2025-06-23 23:36:01 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 5,
  "operationType": "buy",
  "feePayer": "********************************************",
  "recentBlockhash": "********************************************"
}
2025-06-23 23:36:01 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:39:55 error: Test swap error occurred {
  "service": "solana-service",
  "component": "swap",
  "error": "This is a test swap error to verify error logging",
  "tokenAddress": "TEST_TOKEN_ADDRESS",
  "poolAddress": "TEST_POOL_ADDRESS",
  "stack": "Error: This is a test swap error to verify error logging\n    at testSwapErrorLogging (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:9:11)\n    at runLoggerTest (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:26:3)\n    at Object.<anonymous> (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:32:3)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Module.m._compile (/www/wwwroot/redfyn-spot/backend/solana/node_modules/ts-node/src/index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (/www/wwwroot/redfyn-spot/backend/solana/node_modules/ts-node/src/index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"
}
2025-06-23 23:43:27 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 232.045418,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:43:27 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:43:27 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "232.045418 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:43:27 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 232.045418,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-23 23:43:27 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:43:27 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 232.045418,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-23 23:43:27 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:43:27 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30186707377",
  "virtualTokenReserves": "1066363404111554"
}
2025-06-23 23:43:27 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "232.045418 tokens",
  "output": "0.000006504 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:43:29 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "7L4pGLFqtFChckefmxSvXNsa4ZTFmbNiPKbzVf22AMpX"
}
2025-06-23 23:43:29 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:43:37 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:44:17 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 232.045418,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:44:17 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:44:17 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30186707377",
  "virtualTokenReserves": "1066363404111554"
}
2025-06-23 23:44:17 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "232.045418 tokens",
  "output": "0.000006504 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:44:19 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "84WwPakFJQHPW8B2Zb6Mrrme2jibmtMaAsLMpJZDdjEe"
}
2025-06-23 23:44:19 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:44:19 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:44:19 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-23 23:44:26 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "7MYadqGH7KcRewnCR7vxeEEy9dwTs5q5mhp2Px7hEpLKV4z8YtZAZZfv5G46Loc8dxLkfTnHU8qACLYUTBf1REW"
}
2025-06-23 23:44:26 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "7MYadqGH7KcRewnCR7vxeEEy9dwTs5q5mhp2Px7hEpLKV4z8YtZAZZfv5G46Loc8dxLkfTnHU8qACLYUTBf1REW",
  "outputAmount": "0.000006504 SOL"
}
2025-06-23 23:47:06 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 2.320454,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:47:06 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:47:06 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "2.320454 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:47:06 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 2.320454,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-23 23:47:06 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:06 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 2.320454,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-23 23:47:06 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:06 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30044266981",
  "virtualTokenReserves": "1071419051743811"
}
2025-06-23 23:47:06 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "2.320454 tokens",
  "output": "6.6e-8 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:47:08 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "AxmfhYv6QEL5qNW9FovhEBV1xjagA1ivy6bKeHyWsxG6"
}
2025-06-23 23:47:08 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:16 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:47:50 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:47:50 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.15,
  "convertedSlippage": "0.15 (15.00%)",
  "inputPriorityFee": 0.0003,
  "convertedPriorityFee": "3000000 microLamports",
  "inputBribeAmount": 0.0001,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:47:50 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "0.00001 SOL",
  "slippage": "15.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:47:50 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "slippageValue": 0.15,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-23 23:47:50 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:50 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "buy",
  "amount": 0.00001,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:47:50 info: Processing BUY transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:50 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30044266981",
  "virtualTokenReserves": "1071419051743811"
}
2025-06-23 23:47:50 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.00001 SOL",
  "output": "353.047226 tokens",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:47:51 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 5,
  "operationType": "buy",
  "feePayer": "********************************************",
  "recentBlockhash": "CeAz9pzsEZoLVYzb9VYCrdetGby8AdXQ1ofD6x2zNu8u"
}
2025-06-23 23:47:51 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:51 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:51 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-23 23:47:53 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "41bbFNWre8jqPTMigNamYXtL6Fm6zrdR7YDjuVusvytFCyMRyipUmpwabVvxp3CgAzAfSH6Px71JqzUJHJaRUkZj"
}
2025-06-23 23:47:53 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "41bbFNWre8jqPTMigNamYXtL6Fm6zrdR7YDjuVusvytFCyMRyipUmpwabVvxp3CgAzAfSH6Px71JqzUJHJaRUkZj",
  "outputAmount": "353.047226 tokens"
}
2025-06-23 23:47:58 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 2.320454,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:47:58 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:47:58 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30044276881",
  "virtualTokenReserves": "1071418698696585"
}
2025-06-23 23:47:58 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "2.320454 tokens",
  "output": "6.6e-8 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:48:00 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "FNVzya7WZhe3CLyg6F6mF8dHrEs9BfWgtZ3XgUACeZPD"
}
2025-06-23 23:48:00 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:48:00 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:48:00 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-23 23:48:04 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "5F5neQ5HVDJFrwtm6e9Tdj2R8Ji97RaMuKGuHnpBBC2WqPZz5XiryViMkiHQ4LSMU7XfacsS7G3PJZzV9j7Sk1x5"
}
2025-06-23 23:48:04 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "5F5neQ5HVDJFrwtm6e9Tdj2R8Ji97RaMuKGuHnpBBC2WqPZz5XiryViMkiHQ4LSMU7XfacsS7G3PJZzV9j7Sk1x5",
  "outputAmount": "6.6e-8 SOL"
}
2025-06-23 23:48:30 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:48:30 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:48:30 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "88.84778 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:48:30 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-23 23:48:30 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:48:30 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-23 23:48:30 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:48:30 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30044276816",
  "virtualTokenReserves": "1071418701017039"
}
2025-06-23 23:48:30 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002468 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:48:31 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "7d5Pf34JMBXrmUB7qJhVWchUbjGgW1N45UWvGQFBRaLi"
}
2025-06-23 23:48:31 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:48:40 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:49:19 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:49:19 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:20 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023216796",
  "virtualTokenReserves": "1072170256059424"
}
2025-06-23 23:49:20 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002464 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:49:21 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "Hi6GmtdYM72zXtCRtyM8T68sN39azLgS8Jgh8nFAi9vR"
}
2025-06-23 23:49:21 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:21 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:21 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-23 23:49:26 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "627WAA7LvVXLfRztwyPbUUbsUxr1ogCCFPfxEa4b5yfDx3idC26wb4fQcybCoCLb1g8SXG9GcHmexWgx6NpFZqQK"
}
2025-06-23 23:49:26 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "627WAA7LvVXLfRztwyPbUUbsUxr1ogCCFPfxEa4b5yfDx3idC26wb4fQcybCoCLb1g8SXG9GcHmexWgx6NpFZqQK",
  "outputAmount": "0.000002464 SOL"
}
2025-06-23 23:49:40 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-23 23:49:40 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:49:40 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "88.84778 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-23 23:49:40 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-23 23:49:40 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:40 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-23 23:49:40 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:40 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023214309",
  "virtualTokenReserves": "1072170344907204"
}
2025-06-23 23:49:40 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002464 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:49:42 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "72hLkbcHgz6XCdD5mEbhMhohmmBC5QKEUHoeVMXzygh"
}
2025-06-23 23:49:42 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:49:50 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:50:31 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-23 23:50:31 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:50:31 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023214309",
  "virtualTokenReserves": "1072170344907204"
}
2025-06-23 23:50:31 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002464 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-23 23:50:33 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "Fyvc2epTvPQBbnoENZHw9TXC9xPhSWmRK2KPTUsSVZ6p"
}
2025-06-23 23:50:33 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:50:33 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-23 23:50:33 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-23 23:50:37 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "4aptS63H8Qc73sS5Lv6bCTreXTaJrC6v8KR6nU2SXQPcE38hmftqgoLp6cZi1tpNPqeUcuR4qDhxpwEqBQnwqysF"
}
2025-06-23 23:50:37 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "4aptS63H8Qc73sS5Lv6bCTreXTaJrC6v8KR6nU2SXQPcE38hmftqgoLp6cZi1tpNPqeUcuR4qDhxpwEqBQnwqysF",
  "outputAmount": "0.000002464 SOL"
}
2025-06-24 00:00:23 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 43.843776,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-24 00:00:23 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:00:23 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "43.843776 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:00:23 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 43.843776,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-24 00:00:23 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:00:23 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 43.843776,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-24 00:00:23 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:00:23 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023211822",
  "virtualTokenReserves": "1072170433754984"
}
2025-06-24 00:00:23 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "43.843776 tokens",
  "output": "0.000001216 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-24 00:00:26 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "4EknyDzXjvj1KQYmS5TiDHP9e1GYE4G2DuBX5x1FEKDD"
}
2025-06-24 00:00:26 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:00:33 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-24 00:01:16 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 43.843776,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-24 00:01:16 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:01:16 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023211822",
  "virtualTokenReserves": "1072170433754984"
}
2025-06-24 00:01:16 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "43.843776 tokens",
  "output": "0.000001216 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-24 00:01:17 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "DX2rP7GyMAuuZx5FDNedorF4c3v7czP4m8qZdJqWk3pp"
}
2025-06-24 00:01:17 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:01:17 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:01:17 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-24 00:01:21 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "3M4xMFrypMDG46ryqxaTYkKDFzNfvN3HdTCMbeJdaMAcwZQtDsu74xpeZU8j5N435Uo6nneMaay2YcoDdNjJWC2j"
}
2025-06-24 00:01:21 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "3M4xMFrypMDG46ryqxaTYkKDFzNfvN3HdTCMbeJdaMAcwZQtDsu74xpeZU8j5N435Uo6nneMaay2YcoDdNjJWC2j",
  "outputAmount": "0.000001216 SOL"
}
2025-06-24 00:12:24 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 32.882832,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-24 00:12:24 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:12:24 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "32.882832 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:12:24 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 32.882832,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-24 00:12:24 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:12:24 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 32.882832,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-24 00:12:24 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:12:25 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023210595",
  "virtualTokenReserves": "1072170477598760"
}
2025-06-24 00:12:25 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "32.882832 tokens",
  "output": "9.12e-7 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-24 00:12:27 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "2x1Nm9AsJKfijd8UgTWD7peP8KE7j6RvyeUTFp6mtwjc"
}
2025-06-24 00:12:27 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:12:34 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-24 00:13:17 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 32.882832,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-24 00:13:17 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:13:17 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023210595",
  "virtualTokenReserves": "1072170477598760"
}
2025-06-24 00:13:17 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "32.882832 tokens",
  "output": "9.12e-7 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-24 00:13:19 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "97usJ9bkwMk9mzZobko9hJfvCUVhnYgfTqHdqcZEkJqK"
}
2025-06-24 00:13:19 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:13:19 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:13:19 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-24 00:13:21 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "3LzcM9eQdNQXuDZ9Pvv2aJvYkw79aeJPRqCvrxeh2gGMwZLTBjs46ZrmsZ4PE7mondcA4FzgeXJJ4Bo2K5Sm8Van"
}
2025-06-24 00:13:21 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "3LzcM9eQdNQXuDZ9Pvv2aJvYkw79aeJPRqCvrxeh2gGMwZLTBjs46ZrmsZ4PE7mondcA4FzgeXJJ4Bo2K5Sm8Van",
  "outputAmount": "9.12e-7 SOL"
}
2025-06-24 00:14:44 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 97.662012,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-24 00:14:44 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:14:44 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "97.662012 SOL",
  "slippage": "10.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-24 00:14:44 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 97.662012,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-24 00:14:44 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:14:44 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 97.662012,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-24 00:14:44 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:14:44 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30023209675",
  "virtualTokenReserves": "1072170510481592"
}
2025-06-24 00:14:44 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "97.662012 tokens",
  "output": "0.000002708 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-24 00:14:45 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "4Nr5viTTMQQKQ2cK6SAiEBqnFdKpxrDvLZ3SAZ35V8fg"
}
2025-06-24 00:14:45 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:14:45 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-24 00:14:45 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-24 00:14:51 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "DesdD8QvZPhKLrEZ5nr7zHBdkDPYgXTk58SkTHBy7Hp7UxVkuiZWzUVsVeNrCzocXyDpSWrRYqgkBJxSkFLEhF2"
}
2025-06-24 00:14:51 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "DesdD8QvZPhKLrEZ5nr7zHBdkDPYgXTk58SkTHBy7Hp7UxVkuiZWzUVsVeNrCzocXyDpSWrRYqgkBJxSkFLEhF2",
  "outputAmount": "0.000002708 SOL"
}
2025-06-25 03:13:47 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "walletAddress": "34JqtcJJ..."
}
2025-06-25 03:13:47 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.15,
  "convertedSlippage": "0.15 (15.00%)",
  "inputPriorityFee": 0.0003,
  "convertedPriorityFee": "3000000 microLamports",
  "inputBribeAmount": 0.0001,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 03:13:47 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "0.00001 SOL",
  "slippage": "15.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 03:13:47 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "slippageValue": 0.15,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-25 03:13:47 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:13:47 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "buy",
  "amount": 0.00001,
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-25 03:13:47 info: Processing BUY transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:13:47 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "99321654454",
  "virtualTokenReserves": "324098514236243"
}
2025-06-25 03:13:47 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.00001 SOL",
  "output": "32.304889 tokens",
  "price": "0.000000310 SOL per token"
}
2025-06-25 03:13:48 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 5,
  "operationType": "buy",
  "feePayer": "********************************************",
  "recentBlockhash": "JDv8Ea7w5gw7C2FY876BDdCpcaB9XXh4bz7316YLj7W2"
}
2025-06-25 03:13:48 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:13:48 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:13:48 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-25 03:13:52 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "2iG93RkWwf9ap87UWEmdpViNtTjxhi2aPpTKJ2UgJk9AN5XPKjoaR6VDarBSvRpCgCwhm3cbHb4uyj5u8noQDiBx"
}
2025-06-25 03:13:52 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "2iG93RkWwf9ap87UWEmdpViNtTjxhi2aPpTKJ2UgJk9AN5XPKjoaR6VDarBSvRpCgCwhm3cbHb4uyj5u8noQDiBx",
  "outputAmount": "32.304889 tokens"
}
2025-06-25 03:14:55 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "dexType": "pumpfun",
  "amount": 31.98184,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-25 03:14:55 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 03:14:55 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "31.98184 SOL",
  "slippage": "10.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 03:14:55 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "dexType": "pumpfun",
  "amount": 31.98184,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-25 03:14:55 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:14:55 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 31.98184,
  "tokenAddress": "DeWBaStDtgZ7HfhcSj4hBJ5WeemaVqexmStXwKgspump",
  "poolAddress": "Amexbf7vswxxpYpTSWhw8p6upuaKDkXzPfqwdf14Vzuc",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-25 03:14:55 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:14:55 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "99333543439",
  "virtualTokenReserves": "324059723717501"
}
2025-06-25 03:14:55 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "31.98184 tokens",
  "output": "0.000009706 SOL",
  "price": "0.000000303 SOL per token"
}
2025-06-25 03:14:56 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "CbvtQ7RjxXnGF7aAjV7k9GC8zDNeeTvz5nycMdnGD4XR"
}
2025-06-25 03:14:56 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:14:56 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 03:14:56 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-25 03:15:02 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "2HLc186teZRNdfTCvU55t7CsyfYnwo1LHcK9bZxpN8Zg1VRf4WtjkbKZ6AaHCy44m619k3B49aazeH8saVZESAeq"
}
2025-06-25 03:15:02 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "2HLc186teZRNdfTCvU55t7CsyfYnwo1LHcK9bZxpN8Zg1VRf4WtjkbKZ6AaHCy44m619k3B49aazeH8saVZESAeq",
  "outputAmount": "0.000009706 SOL"
}
2025-06-25 06:15:37 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "BXdFSCkf5Y9earM8a5Q2G1pyEtgFDhy8Eb66dyaVpump",
  "poolAddress": "FpRQB8WEasvpxBzjY4tsWcgWHHKjUu2qpdJq13JtsspK",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "walletAddress": "34JqtcJJ..."
}
2025-06-25 06:15:37 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.15,
  "convertedSlippage": "0.15 (15.00%)",
  "inputPriorityFee": 0.0003,
  "convertedPriorityFee": "3000000 microLamports",
  "inputBribeAmount": 0.0001,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 06:15:37 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "low",
  "mevRecommended": true,
  "requestedMevProtection": false,
  "tradeValue": "0.00001 SOL",
  "slippage": "15.00%",
  "finalMevProtection": false,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-25 06:15:37 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "BXdFSCkf5Y9earM8a5Q2G1pyEtgFDhy8Eb66dyaVpump",
  "poolAddress": "FpRQB8WEasvpxBzjY4tsWcgWHHKjUu2qpdJq13JtsspK",
  "dexType": "pumpfun",
  "amount": 0.00001,
  "direction": "buy",
  "slippageValue": 0.15,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": false,
  "priorityLevel": "low"
}
2025-06-25 06:15:37 info: MEV Protection explicitly disabled - using regular Privy execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 06:15:37 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "buy",
  "amount": 0.00001,
  "tokenAddress": "BXdFSCkf5Y9earM8a5Q2G1pyEtgFDhy8Eb66dyaVpump",
  "poolAddress": "FpRQB8WEasvpxBzjY4tsWcgWHHKjUu2qpdJq13JtsspK",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-25 06:15:37 info: Processing BUY transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 06:15:37 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30014633739",
  "virtualTokenReserves": "1072476855379471"
}
2025-06-25 06:15:37 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.00001 SOL",
  "output": "353.744692 tokens",
  "price": "0.000000028 SOL per token"
}
2025-06-25 06:15:38 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 5,
  "operationType": "buy",
  "feePayer": "********************************************",
  "recentBlockhash": "4RAbxdECqarvi6LUumo8rHKKtKkHZHZ7qryqqrfMiVdj"
}
2025-06-25 06:15:38 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 06:15:38 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-25 06:15:38 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "low"
}
2025-06-25 06:15:39 info: Transaction signed and sent successfully {
  "service": "solana-service",
  "component": "swap",
  "signature": "4WChvaCZZYpgciP79Tu94NMrJdNge6KSxZsqoJDKaKRaREGJPeqoCBTUyG5vdUvk2PZFTv5LouJEy5bm2AwPx83X"
}
2025-06-25 06:15:39 info: Swap executed successfully {
  "service": "solana-service",
  "component": "swap",
  "executionMethod": "privy",
  "signature": "4WChvaCZZYpgciP79Tu94NMrJdNge6KSxZsqoJDKaKRaREGJPeqoCBTUyG5vdUvk2PZFTv5LouJEy5bm2AwPx83X",
  "outputAmount": "353.744692 tokens"
}
2025-06-27 19:15:10 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-27 19:15:10 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-27 19:15:10 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "88.84778 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-27 19:15:10 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 88.84778,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-27 19:15:10 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:10 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-27 19:15:10 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:11 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30001058893",
  "virtualTokenReserves": "1072962130028349"
}
2025-06-27 19:15:11 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002461 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-27 19:15:11 error: Error in PumpFun sell calculation {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeWithJito (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:135:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:84:28)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:11 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeWithJito (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:135:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:84:28)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:11 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 88.84778,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-27 19:15:11 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:11 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30001058893",
  "virtualTokenReserves": "1072962130028349"
}
2025-06-27 19:15:11 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "88.84778 tokens",
  "output": "0.000002461 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-27 19:15:12 error: Error in PumpFun sell calculation {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:12 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:12 error: Swap execution failed {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "dexType": "pumpfun",
  "direction": "sell"
}
2025-06-27 19:15:26 info: Executing swap request {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.5,
  "direction": "sell",
  "walletAddress": "34JqtcJJ..."
}
2025-06-27 19:15:26 info: Enhanced parameter conversion completed {
  "service": "solana-service",
  "component": "swap",
  "inputSlippage": 0.1,
  "convertedSlippage": "0.1 (10.00%)",
  "inputPriorityFee": 0.0002,
  "convertedPriorityFee": "2000000 microLamports",
  "inputBribeAmount": 0.00005,
  "convertedBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-27 19:15:26 info: MEV protection settings {
  "service": "solana-service",
  "component": "swap",
  "priorityLevel": "high",
  "mevRecommended": true,
  "requestedMevProtection": true,
  "tradeValue": "0.5 SOL",
  "slippage": "10.00%",
  "finalMevProtection": true,
  "finalBribeAmount": "100000 lamports (0.000100 SOL)"
}
2025-06-27 19:15:26 info: Creating swap transaction {
  "service": "solana-service",
  "component": "swap",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "dexType": "pumpfun",
  "amount": 0.5,
  "direction": "sell",
  "slippageValue": 0.1,
  "walletAddress": "34JqtcJJ...",
  "mevProtection": true,
  "priorityLevel": "high"
}
2025-06-27 19:15:26 info: MEV Protection enabled (high priority) - using Jito execution {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:26 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 0.5,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": false
}
2025-06-27 19:15:26 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:26 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30001058893",
  "virtualTokenReserves": "1072962130028349"
}
2025-06-27 19:15:26 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.5 tokens",
  "output": "1.4e-8 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-27 19:15:27 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "C9t6mgn6QCP9Ys3AarnwqcrLxeY6YzBi8V2Un5Kj7Etq"
}
2025-06-27 19:15:27 info: Transaction created for external execution (Jito) {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:15:36 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-27 19:16:18 info: Starting corrected PumpFun swap execution {
  "service": "solana-service",
  "component": "swap",
  "direction": "sell",
  "amount": 0.5,
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "poolAddress": "AoFuTGHPraZtPyyVJjsgNTcAuwLKK4Q2Ly593eBuLh2G",
  "walletAddress": "34JqtcJJ...",
  "executeTransaction": true
}
2025-06-27 19:16:18 info: Processing SELL transaction {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:16:18 debug: Bonding curve reserves retrieved {
  "service": "solana-service",
  "component": "swap",
  "virtualSolReserves": "30001058893",
  "virtualTokenReserves": "1072962130028349"
}
2025-06-27 19:16:18 info: Bonding curve calculation completed {
  "service": "solana-service",
  "component": "swap",
  "input": "0.5 tokens",
  "output": "1.4e-8 SOL",
  "price": "0.000000028 SOL per token"
}
2025-06-27 19:16:19 info: Transaction created successfully {
  "service": "solana-service",
  "component": "swap",
  "instructionCount": 4,
  "operationType": "sell",
  "feePayer": "********************************************",
  "recentBlockhash": "********************************************"
}
2025-06-27 19:16:19 info: User will sign via Privy, platform covers gas costs through reimbursement {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:16:19 debug: Transaction serialized for Privy session signing {
  "service": "solana-service",
  "component": "swap"
}
2025-06-27 19:16:19 info: Signing transaction with Privy {
  "service": "solana-service",
  "component": "swap",
  "walletId": "cbq2lb54zo7rtzv14i5sp75j",
  "walletAddress": "34JqtcJJ...",
  "priorityLevel": "medium"
}
2025-06-27 19:16:21 error: Error during transaction signing or sending {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "walletAddress": "34JqtcJJ...",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:16:21 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:16:21 error: Swap execution failed {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "dexType": "pumpfun",
  "direction": "sell"
}
