2025-06-23 23:39:55 error: Test swap error occurred {
  "service": "solana-service",
  "component": "swap",
  "error": "This is a test swap error to verify error logging",
  "tokenAddress": "TEST_TOKEN_ADDRESS",
  "poolAddress": "TEST_POOL_ADDRESS",
  "stack": "Error: This is a test swap error to verify error logging\n    at testSwapErrorLogging (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:9:11)\n    at runLoggerTest (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:26:3)\n    at Object.<anonymous> (/www/wwwroot/redfyn-spot/backend/solana/src/utils/test-logger.ts:32:3)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Module.m._compile (/www/wwwroot/redfyn-spot/backend/solana/node_modules/ts-node/src/index.ts:1618:23)\n    at node:internal/modules/cjs/loader:1895:10\n    at Object.require.extensions.<computed> [as .ts] (/www/wwwroot/redfyn-spot/backend/solana/node_modules/ts-node/src/index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)"
}
2025-06-23 23:43:37 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:47:16 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:48:40 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-23 23:49:50 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-24 00:00:33 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-24 00:12:34 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-27 19:15:11 error: Error in PumpFun sell calculation {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeWithJito (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:135:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:84:28)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:11 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeWithJito (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:135:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:84:28)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:12 error: Error in PumpFun sell calculation {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:12 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:15:12 error: Swap execution failed {
  "service": "solana-service",
  "component": "swap",
  "error": "Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.",
  "stack": "Error: Insufficient token balance: User has 0.986485 tokens, requested to sell 88.84778 tokens. Shortfall: 87.861295 tokens.\n    at createCorrectedPumpFunSellTransaction (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:256:13)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:569:23)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "dexType": "pumpfun",
  "direction": "sell"
}
2025-06-27 19:15:36 error: API Timeout Exceeded {
  "service": "solana-service",
  "component": "swap",
  "route": "/api/pump/swap",
  "method": "POST",
  "timeoutMs": 10000
}
2025-06-27 19:16:21 error: Error during transaction signing or sending {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "walletAddress": "34JqtcJJ...",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:16:21 error: Error in executeCorrectedPumpFunSwap {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "direction": "sell",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)"
}
2025-06-27 19:16:21 error: Swap execution failed {
  "service": "solana-service",
  "component": "swap",
  "error": "Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773",
  "stack": "Error: Failed to sign transaction with Privy: Privy API error (400): Error broadcasting transaction with message: Error: Transaction simulation failed: Error processing Instruction 3: custom program error: 0x1773 Logs: Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ComputeBudget111111111111111111111111111111 invoke [1] -- Program ComputeBudget111111111111111111111111111111 success -- Program ******************************** invoke [1] -- Program ******************************** success -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P invoke [1] -- Program log: Instruction: Sell -- Program log: AnchorError thrown in programs/pump/src/lib.rs:449. Error Code: TooLittleSolReceived. Error Number: 6003. Error Message: slippage: Too little SOL received to sell the given amount of tokens.. -- Program log: Left: 11 -- Program log: Right: 12 -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P consumed 25401 of 99550 compute units -- Program 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P failed: custom program error: 0x1773\n    at signAndSendTransactionWithPrivy (/www/wwwroot/redfyn-spot/backend/solana/src/services/privy/proper-privy.service.ts:299:11)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async executeCorrectedPumpFunSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/pumpFun/corrected-pump.service.ts:630:25)\n    at async executeRegular (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:272:22)\n    at async executeEnhancedSwap (/www/wwwroot/redfyn-spot/backend/solana/src/services/mev/enhanced-swap.service.ts:93:31)\n    at async executeSwap (/www/wwwroot/redfyn-spot/backend/solana/src/controllers/pumpFun/pump.controller.ts:451:18)",
  "tokenAddress": "DWHTop2JWSWmeXVWBphDsYsMeoTRKh1s7FsXUEjSpump",
  "dexType": "pumpfun",
  "direction": "sell"
}
