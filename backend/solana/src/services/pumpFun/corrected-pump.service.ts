import { Connection, PublicKey, Transaction, TransactionInstruction, SystemProgram, ComputeBudgetProgram } from '@solana/web3.js';
import { getAssociatedTokenAddress, createAssociatedTokenAccountIdempotentInstruction, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import { SwapRequest, QuoteResult, SwapDirection } from '../../types/pump.types';
import { swapLogger } from '../../utils/logger';

import {
  calculatePlatformFee,
  solToLamports,
  PUMPFUN_SWAP_FEE_RATIO
} from './utils.service';
import { config } from '../../config';

// Constants from working implementation
const PUMP_FUN_PROGRAM_ID = new PublicKey('6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P');
const PUMP_FUN_GLOBAL = new PublicKey('4wTV1YmiEkRvAtNtsSGPtUrqRYQMe5SKy2uB4Jjaxnjf');
const PUMP_FUN_EVENT_AUTHORITY = new PublicKey('Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1');
const PUMP_FUN_FEE_RECIPIENT = new PublicKey('CebN5WGQ4jvEPvsVU4EoHEpgzq1VV7AbicfhtW4xC9iM');

// Instruction discriminators
const BUY_DISCRIMINATOR = Buffer.from('66063d1201daebea', 'hex');
const SELL_DISCRIMINATOR = Buffer.from('33e685a4017f83ad', 'hex');

/**
 * Generate PDA for bonding curve (following working implementation)
 */
function getBondingCurvePDA(mint: PublicKey): PublicKey {
  const [bondingCurve] = PublicKey.findProgramAddressSync(
    [Buffer.from('bonding-curve'), mint.toBuffer()],
    PUMP_FUN_PROGRAM_ID
  );
  return bondingCurve;
}

/**
 * Get associated token account for bonding curve (following working implementation)
 */
async function getAssociatedBondingCurve(mint: PublicKey): Promise<PublicKey> {
  const bondingCurve = getBondingCurvePDA(mint);
  return getAssociatedTokenAddress(mint, bondingCurve, true);
}

/**
 * Get creator from bonding curve state (fetch actual creator from on-chain data)
 */
async function getCreatorFromBondingCurve(
  connection: Connection,
  bondingCurve: PublicKey,
  fallback: PublicKey
): Promise<PublicKey> {
  try {
    const accountInfo = await connection.getAccountInfo(bondingCurve);
    if (accountInfo && accountInfo.data) {
      // Parse the bonding curve state to get the creator
      // Skip discriminator (8 bytes) + virtualTokenReserves(8) + virtualSolReserves(8) + realTokenReserves(8) + realSolReserves(8) + tokenTotalSupply(8) + complete(1)
      const creatorOffset = 8 + 8 + 8 + 8 + 8 + 8 + 1; // 49 bytes
      if (accountInfo.data.length >= creatorOffset + 32) {
        const creatorBytes = Buffer.from(accountInfo.data.buffer, accountInfo.data.byteOffset + creatorOffset, 32);
        return new PublicKey(creatorBytes);
      }
    }
  } catch (error) {
    console.warn('Could not fetch creator from bonding curve, using fallback');
  }
  return fallback;
}

/**
 * Generate PDA for creator vault (using actual creator from bonding curve)
 * Note: Using 'creator-vault' seed as per IDL specification
 */
function getCreatorVaultPDA(creator: PublicKey): PublicKey {
  const [creatorVault] = PublicKey.findProgramAddressSync(
    [Buffer.from('creator-vault'), creator.toBuffer()],
    PUMP_FUN_PROGRAM_ID
  );
  return creatorVault;
}

/**
 * Create a corrected PumpFun buy transaction (following working implementation)
 */
export async function createCorrectedPumpFunBuyTransaction(
  connection: Connection,
  swapRequest: SwapRequest,
  quoteResult: QuoteResult
): Promise<Transaction> {
  console.log('Creating corrected PumpFun buy transaction...');

  const mint = new PublicKey(swapRequest.tokenAddress);
  const userWallet = new PublicKey(swapRequest.walletAddress);
  const bondingCurve = getBondingCurvePDA(mint);
  const associatedBondingCurve = await getAssociatedBondingCurve(mint);
  const userTokenAccount = await getAssociatedTokenAddress(mint, userWallet);

  // Get creator from bonding curve data (fetch the actual creator from on-chain data)
  const creator = await getCreatorFromBondingCurve(connection, bondingCurve, PUMP_FUN_FEE_RECIPIENT);
  // Use actual creator from bonding curve for vault derivation (CORRECT!)
  const creatorVault = getCreatorVaultPDA(creator);

  console.log('Account addresses:');
  console.log('- Mint:', mint.toString());
  console.log('- User wallet:', userWallet.toString());
  console.log('- Bonding curve:', bondingCurve.toString());
  console.log('- Associated bonding curve:', associatedBondingCurve.toString());
  console.log('- User token account:', userTokenAccount.toString());
  console.log('- Creator:', creator.toString());
  console.log('- Creator vault:', creatorVault.toString());

  // Calculate amounts
  const tokenAmountRaw = BigInt(Math.floor(quoteResult.outAmount * Math.pow(10, 6))); // 6 decimals

  // Apply slippage to maximum SOL amount (CRITICAL FIX for small amounts)
  const requestedSlippage = swapRequest.slippage || 0.05; // Default 5% slippage if not provided
  const slippageMultiplier = 1 + requestedSlippage; // Add slippage for buy (we're willing to pay more)
  const maxSolWithSlippage = swapRequest.amount * slippageMultiplier;
  const maxAmountLamports = BigInt(Math.ceil(maxSolWithSlippage * 1_000_000_000)); // Use Math.ceil to round up

  console.log(`Input SOL amount: ${swapRequest.amount} SOL`);
  console.log(`Slippage: ${requestedSlippage * 100}%`);
  console.log(`Max SOL with slippage: ${maxSolWithSlippage} SOL`);
  console.log(`Max amount in lamports: ${maxAmountLamports} lamports`);

  // Prepare instruction data
  const dataBuffer = Buffer.alloc(16);
  dataBuffer.writeBigUInt64LE(tokenAmountRaw, 0);
  dataBuffer.writeBigUInt64LE(maxAmountLamports, 8);
  const buyInstructionData = Buffer.concat([BUY_DISCRIMINATOR, dataBuffer]);

  // Build accounts array - FIXED order based on PumpFun program expectations
  const accounts = [
    { pubkey: PUMP_FUN_GLOBAL, isSigner: false, isWritable: false },           // 0: Global config
    { pubkey: PUMP_FUN_FEE_RECIPIENT, isSigner: false, isWritable: true },     // 1: Fee recipient
    { pubkey: mint, isSigner: false, isWritable: false },                      // 2: Token mint
    { pubkey: bondingCurve, isSigner: false, isWritable: true },               // 3: Bonding curve
    { pubkey: associatedBondingCurve, isSigner: false, isWritable: true },     // 4: Associated bonding curve
    { pubkey: userTokenAccount, isSigner: false, isWritable: true },           // 5: User token account
    { pubkey: userWallet, isSigner: true, isWritable: true },                  // 6: User wallet (MUST be signer per IDL)
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },   // 7: System program
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },          // 8: Token program (CRITICAL POSITION)
    { pubkey: creatorVault, isSigner: false, isWritable: true },               // 9: Creator vault (CRITICAL POSITION)
    { pubkey: PUMP_FUN_EVENT_AUTHORITY, isSigner: false, isWritable: false },  // 10: Event authority
    { pubkey: PUMP_FUN_PROGRAM_ID, isSigner: false, isWritable: false },       // 11: Program ID
  ];

  console.log('Buy instruction accounts:');
  accounts.forEach((acc, idx) => {
    console.log(`  ${idx}: ${acc.pubkey.toString()} (signer: ${acc.isSigner}, writable: ${acc.isWritable})`);
  });

  // Calculate platform fee (1% of transaction amount)
  const platformFeeLamports = calculatePlatformFee(solToLamports(swapRequest.amount), 'pumpfun');
  console.log(`Platform fee: ${platformFeeLamports} lamports`);

  // Create platform fee transfer instruction (user pays platform fees)
  const platformFeeInstruction = SystemProgram.transfer({
    fromPubkey: userWallet,
    toPubkey: config.platformFeeWallet,
    lamports: platformFeeLamports,
  });
  console.log(`Platform fee transfer: ${platformFeeLamports} lamports from ${userWallet.toString()} to ${config.platformFeeWallet.toString()}`);

  // Note: Platform keypair is not needed for PumpFun swaps as user signs via Privy
  // Platform only collects fees through the platform fee transfer instruction

  // Add Compute Budget Instructions for better transaction success rates (matching pump.service.ts)
  const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
    units: 100000 // Standard limit for PumpFun transactions
  });

  const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
    microLamports: 200000 // Priority fee for faster processing (~0.0002 SOL, much more reasonable)
  });

  const instructions = [
    // 1. Compute budget instructions MUST come first (for transaction priority)
    computeUnitLimitIx,
    computeUnitPriceIx,
    // 2. Platform fee transfer (user pays platform fees)
    platformFeeInstruction,
    // 3. Create associated token account if needed (idempotent - won't fail if exists)
    createAssociatedTokenAccountIdempotentInstruction(
      userWallet, // User pays for ATA creation (consistent with fee payer)
      userTokenAccount,
      userWallet,
      mint
    ),
    // 4. PumpFun buy instruction
    new TransactionInstruction({
      keys: accounts,
      programId: PUMP_FUN_PROGRAM_ID,
      data: buyInstructionData,
    })
  ];

  const transaction = new Transaction();
  transaction.add(...instructions);

  // For Privy session signing, user must be fee payer but platform can partially sign
  // This allows user to sign via Privy while platform covers gas costs
  transaction.feePayer = userWallet;

  console.log('Transaction created successfully');
  console.log(`- Instructions: ${transaction.instructions.length} (1: Compute limit, 2: Compute price, 3: Platform fee, 4: ATA creation, 5: PumpFun buy)`);
  console.log(`- Fee payer: ${transaction.feePayer.toString()}`);
  console.log(`- Recent blockhash: ${transaction.recentBlockhash}`);

  return transaction;
}

/**
 * Create a corrected PumpFun sell transaction (following working implementation)
 */
export async function createCorrectedPumpFunSellTransaction(
  connection: Connection,
  swapRequest: SwapRequest,
  quoteResult: QuoteResult
): Promise<Transaction> {
  console.log('Creating corrected PumpFun sell transaction...');

  const mint = new PublicKey(swapRequest.tokenAddress);
  const userWallet = new PublicKey(swapRequest.walletAddress);
  const bondingCurve = getBondingCurvePDA(mint);
  const associatedBondingCurve = await getAssociatedBondingCurve(mint);
  const userTokenAccount = await getAssociatedTokenAddress(mint, userWallet);

  // STEP 1: Validate user's token balance before proceeding
  console.log('=== TOKEN BALANCE VALIDATION ===');
  console.log(`Checking token balance for user: ${userWallet.toString()}`);
  console.log(`Token mint: ${mint.toString()}`);
  console.log(`User token account: ${userTokenAccount.toString()}`);
  console.log(`Requested sell amount: ${swapRequest.amount} tokens`);

  try {
    // Check if the user's token account exists
    const tokenAccountInfo = await connection.getAccountInfo(userTokenAccount);

    if (!tokenAccountInfo) {
      const errorMessage = `Token account does not exist for user ${userWallet.toString()}. User has no tokens to sell.`;
      console.error(errorMessage);
      throw new Error(`Insufficient token balance: User has 0 tokens, requested to sell ${swapRequest.amount} tokens. Shortfall: ${swapRequest.amount} tokens.`);
    }

    // Get the user's current token balance
    const tokenBalance = await connection.getTokenAccountBalance(userTokenAccount);
    const currentBalance = parseFloat(tokenBalance.value.uiAmountString || '0');

    console.log(`Current token balance: ${currentBalance} tokens`);
    console.log(`Token balance (raw): ${tokenBalance.value.amount}`);
    console.log(`Token decimals: ${tokenBalance.value.decimals}`);

    // Validate that user has sufficient tokens
    if (currentBalance < swapRequest.amount) {
      const shortfall = swapRequest.amount - currentBalance;
      const errorMessage = `Insufficient token balance: User has ${currentBalance} tokens, requested to sell ${swapRequest.amount} tokens. Shortfall: ${shortfall.toFixed(6)} tokens.`;
      console.error(errorMessage);
      throw new Error(errorMessage);
    }

    console.log(`✅ Token balance validation passed: User has ${currentBalance} tokens, selling ${swapRequest.amount} tokens`);
    console.log(`Remaining balance after sell: ${(currentBalance - swapRequest.amount).toFixed(6)} tokens`);

  } catch (error: any) {
    if (error.message && error.message.includes('Insufficient token balance')) {
      throw error; // Re-throw our custom error
    }
    // Handle other errors (network issues, etc.)
    console.error('Error validating token balance:', error);
    throw new Error(`Failed to validate token balance: ${error?.message || 'Unknown error'}`);
  }

  // Get creator from bonding curve data (fetch the actual creator from on-chain data)
  const creator = await getCreatorFromBondingCurve(connection, bondingCurve, PUMP_FUN_FEE_RECIPIENT);
  // Use actual creator from bonding curve for vault derivation (CORRECT!)
  const creatorVault = getCreatorVaultPDA(creator);

  console.log('Account addresses:');
  console.log('- Mint:', mint.toString());
  console.log('- User wallet:', userWallet.toString());
  console.log('- Bonding curve:', bondingCurve.toString());
  console.log('- Associated bonding curve:', associatedBondingCurve.toString());
  console.log('- User token account:', userTokenAccount.toString());
  console.log('- Creator:', creator.toString());
  console.log('- Creator vault:', creatorVault.toString());

  // Calculate amounts for sell
  const tokenAmountRaw = BigInt(Math.floor(swapRequest.amount * Math.pow(10, 6))); // Token amount to sell (6 decimals)

  // Apply slippage protection to minimum SOL output (CRITICAL FIX!)
  // Use a more conservative slippage for small amounts
  const requestedSlippage = swapRequest.slippage || 0.05; // Default 5% slippage if not provided
  const slippageMultiplier = 1 - requestedSlippage;
  const minSolOutputWithSlippage = quoteResult.outAmount * slippageMultiplier;
  const minSolOutput = BigInt(Math.floor(minSolOutputWithSlippage * 1_000_000_000)); // Min SOL output in lamports with slippage

  // Ensure minimum output is at least 2 lamports to avoid slippage check failures
  const finalMinSolOutput = minSolOutput < 2n ? 2n : minSolOutput;

  console.log(`Expected SOL output: ${quoteResult.outAmount} SOL`);
  console.log(`Slippage: ${requestedSlippage * 100}%`);
  console.log(`Min SOL output with slippage: ${minSolOutputWithSlippage} SOL (${minSolOutput} lamports)`);
  console.log(`Final min SOL output (adjusted): ${finalMinSolOutput} lamports`);

  // Prepare instruction data for sell
  const dataBuffer = Buffer.alloc(16);
  dataBuffer.writeBigUInt64LE(tokenAmountRaw, 0);
  dataBuffer.writeBigUInt64LE(finalMinSolOutput, 8);
  const sellInstructionData = Buffer.concat([SELL_DISCRIMINATOR, dataBuffer]);

  // Build accounts array - same order as buy but for sell operation
  const accounts = [
    { pubkey: PUMP_FUN_GLOBAL, isSigner: false, isWritable: false },           // 0: Global config
    { pubkey: PUMP_FUN_FEE_RECIPIENT, isSigner: false, isWritable: true },     // 1: Fee recipient
    { pubkey: mint, isSigner: false, isWritable: false },                      // 2: Token mint
    { pubkey: bondingCurve, isSigner: false, isWritable: true },               // 3: Bonding curve
    { pubkey: associatedBondingCurve, isSigner: false, isWritable: true },     // 4: Associated bonding curve
    { pubkey: userTokenAccount, isSigner: false, isWritable: true },           // 5: User token account
    { pubkey: userWallet, isSigner: true, isWritable: true },                  // 6: User wallet (MUST be signer per IDL)
    { pubkey: SystemProgram.programId, isSigner: false, isWritable: false },   // 7: System program
    { pubkey: creatorVault, isSigner: false, isWritable: true },               // 8: Creator vault (CRITICAL POSITION - FIXED ORDER)
    { pubkey: TOKEN_PROGRAM_ID, isSigner: false, isWritable: false },          // 9: Token program (CRITICAL POSITION - FIXED ORDER)
    { pubkey: PUMP_FUN_EVENT_AUTHORITY, isSigner: false, isWritable: false },  // 10: Event authority
    { pubkey: PUMP_FUN_PROGRAM_ID, isSigner: false, isWritable: false },       // 11: Program ID
  ];

  console.log('Sell instruction accounts:');
  accounts.forEach((acc, idx) => {
    console.log(`  ${idx}: ${acc.pubkey.toString()} (signer: ${acc.isSigner}, writable: ${acc.isWritable})`);
  });

  // Calculate platform fee (1% of SOL output amount)
  const platformFeeLamports = calculatePlatformFee(finalMinSolOutput, 'pumpfun');
  console.log(`Platform fee: ${platformFeeLamports} lamports`);

  // For sell transactions, we need to check if user has enough SOL to pay the platform fee
  // If not, we'll deduct the platform fee from the swap output instead of requiring separate payment

  // Check user's SOL balance
  const userBalance = await connection.getBalance(userWallet);
  console.log(`User SOL balance: ${userBalance} lamports`);

  // Estimate transaction fee (5000 lamports is a reasonable estimate for a simple transaction)
  const estimatedTxFee = 5000n;
  const requiredBalance = platformFeeLamports + estimatedTxFee;

  console.log(`Required balance for platform fee + tx fee: ${requiredBalance} lamports`);
  console.log(`User has sufficient balance: ${userBalance >= requiredBalance}`);

  // Add Compute Budget Instructions for better transaction success rates (matching pump.service.ts)
  const computeUnitLimitIx = ComputeBudgetProgram.setComputeUnitLimit({
    units: 100000 // Standard limit for PumpFun transactions
  });

  const computeUnitPriceIx = ComputeBudgetProgram.setComputeUnitPrice({
    microLamports: 200000 // Priority fee for faster processing (~0.0002 SOL, much more reasonable)
  });

  let instructions: TransactionInstruction[];

  if (BigInt(userBalance) >= requiredBalance) {
    // User has enough SOL to pay platform fee separately
    console.log('User has sufficient SOL balance - charging platform fee separately');

    const platformFeeInstruction = SystemProgram.transfer({
      fromPubkey: userWallet,
      toPubkey: config.platformFeeWallet,
      lamports: platformFeeLamports,
    });
    console.log(`Platform fee transfer: ${platformFeeLamports} lamports from ${userWallet.toString()} to ${config.platformFeeWallet.toString()}`);

    instructions = [
      // 1. Compute budget instructions MUST come first (for transaction priority)
      computeUnitLimitIx,
      computeUnitPriceIx,
      // 2. Platform fee transfer (user pays platform fees)
      platformFeeInstruction,
      // 3. PumpFun sell instruction
      new TransactionInstruction({
        keys: accounts,
        programId: PUMP_FUN_PROGRAM_ID,
        data: sellInstructionData,
      })
    ];
  } else {
    // User doesn't have enough SOL - platform fee will be deducted from swap output
    console.log('User has insufficient SOL balance - platform fee will be deducted from swap output');
    console.log('Note: Platform fee will be collected when user receives SOL from the swap');

    instructions = [
      // 1. Compute budget instructions MUST come first (for transaction priority)
      computeUnitLimitIx,
      computeUnitPriceIx,
      // 2. PumpFun sell instruction - platform fee will be handled post-swap
      new TransactionInstruction({
        keys: accounts,
        programId: PUMP_FUN_PROGRAM_ID,
        data: sellInstructionData,
      })
    ];
  }

  const transaction = new Transaction();
  transaction.add(...instructions);

  // For Privy session signing, user must be fee payer
  transaction.feePayer = userWallet;

  console.log('Transaction created successfully');
  if (instructions.length === 4) {
    console.log(`- Instructions: ${transaction.instructions.length} (1: Compute limit, 2: Compute price, 3: Platform fee, 4: PumpFun sell)`);
  } else {
    console.log(`- Instructions: ${transaction.instructions.length} (1: Compute limit, 2: Compute price, 3: PumpFun sell only - platform fee deducted from output)`);
  }
  console.log(`- Fee payer: ${transaction.feePayer.toString()}`);
  console.log(`- Recent blockhash: ${transaction.recentBlockhash}`);

  return transaction;
}

/**
 * Execute a corrected PumpFun swap with Privy session signing for user and platform paying gas
 */
export async function executeCorrectedPumpFunSwap(
  swapRequest: SwapRequest,
  executeTransaction: boolean = true
): Promise<{ signature?: string; transaction?: string; quoteResult: QuoteResult }> {
  try {
    swapLogger.info('Starting corrected PumpFun swap execution', { 
      direction: swapRequest.direction,
      amount: swapRequest.amount,
      tokenAddress: swapRequest.tokenAddress,
      poolAddress: swapRequest.poolAddress,
      walletAddress: swapRequest.walletAddress.substring(0, 8) + '...',
      executeTransaction
    });

    if (!process.env.SOLANA_RPC_URL) {
      const error = new Error('SOLANA_RPC_URL environment variable is not set');
      swapLogger.error('Missing RPC URL configuration', { error: error.message });
      throw error;
    }

    const connection = new Connection(process.env.SOLANA_RPC_URL, 'confirmed');

    // Create quote result based on direction
    let quoteResult: QuoteResult;
    let transaction: Transaction;

    if (swapRequest.direction === SwapDirection.Buy) {
      // For buy: SOL -> Token - Use REAL bonding curve calculation like the quote endpoint
      swapLogger.info('Processing BUY transaction');
      const mint = new PublicKey(swapRequest.tokenAddress);
      const bondingCurve = getBondingCurvePDA(mint);

      try {
        // Fetch real bonding curve data
        const bondingCurveAccount = await connection.getAccountInfo(bondingCurve);
        if (!bondingCurveAccount || !bondingCurveAccount.data) {
          const error = new Error(`Bonding curve account not found: ${bondingCurve.toString()}`);
          swapLogger.error('Bonding curve account not found', { 
            bondingCurve: bondingCurve.toString(),
            tokenAddress: swapRequest.tokenAddress
          });
          throw error;
        }

        // Parse bonding curve state (following working implementation)
        const dataView = new DataView(bondingCurveAccount.data.buffer, bondingCurveAccount.data.byteOffset + 8);
        const virtualTokenReserves = dataView.getBigUint64(0, true);
        const virtualSolReserves = dataView.getBigUint64(8, true);

        swapLogger.debug('Bonding curve reserves retrieved', {
          virtualSolReserves: virtualSolReserves.toString(),
          virtualTokenReserves: virtualTokenReserves.toString()
        });

        // Use proper bonding curve calculation like the quote endpoint
        // Import the same calculation function used by the quote endpoint
        const { calculateBondingCurveOutput, solToLamports, rawToToken } = await import('./utils.service');

        const inputLamports = solToLamports(swapRequest.amount);
        const outputAmountRaw = calculateBondingCurveOutput(
          virtualSolReserves,
          virtualTokenReserves,
          inputLamports,
          true, // isBuy
          PUMPFUN_SWAP_FEE_RATIO // Use the same fee ratio as quote
        );

        const outAmount = rawToToken(outputAmountRaw, 6); // 6 decimals for PumpFun tokens
        const price = swapRequest.amount / outAmount;

        swapLogger.info('Bonding curve calculation completed', { 
          input: `${swapRequest.amount} SOL`,
          output: `${outAmount} tokens`,
          price: `${price.toFixed(9)} SOL per token`
        });

        quoteResult = {
          outAmount,
          price,
          outputAmountRaw
        };
        
        transaction = await createCorrectedPumpFunBuyTransaction(connection, swapRequest, quoteResult);
      } catch (error: any) {
        swapLogger.error('Error in PumpFun buy calculation', { 
          error: error.message,
          tokenAddress: swapRequest.tokenAddress,
          stack: error.stack
        });
        throw error;
      }
    } else {
      // For sell: Token -> SOL - Use REAL bonding curve calculation
      swapLogger.info('Processing SELL transaction');
      const mint = new PublicKey(swapRequest.tokenAddress);
      const bondingCurve = getBondingCurvePDA(mint);

      try {
        // Fetch real bonding curve data
        const bondingCurveAccount = await connection.getAccountInfo(bondingCurve);
        if (!bondingCurveAccount || !bondingCurveAccount.data) {
          const error = new Error(`Bonding curve account not found: ${bondingCurve.toString()}`);
          swapLogger.error('Bonding curve account not found', {
            bondingCurve: bondingCurve.toString(),
            tokenAddress: swapRequest.tokenAddress
          });
          throw error;
        }

        // Parse bonding curve state (following working implementation)
        const dataView = new DataView(bondingCurveAccount.data.buffer, bondingCurveAccount.data.byteOffset + 8);
        const virtualTokenReserves = dataView.getBigUint64(0, true);
        const virtualSolReserves = dataView.getBigUint64(8, true);

        swapLogger.debug('Bonding curve reserves retrieved', {
          virtualSolReserves: virtualSolReserves.toString(),
          virtualTokenReserves: virtualTokenReserves.toString()
        });

        // Use proper bonding curve calculation like the quote endpoint (for sell operations)
        // Import the same calculation function used by the quote endpoint
        const { calculateBondingCurveOutput, tokenToRaw, rawToToken, SOL_DECIMALS } = await import('./utils.service');

        const inputTokensRaw = tokenToRaw(swapRequest.amount, 6); // Convert tokens to raw (6 decimals for PumpFun)
        const outputAmountRaw = calculateBondingCurveOutput(
          virtualSolReserves,
          virtualTokenReserves,
          inputTokensRaw,
          false, // isBuy = false for sell
          PUMPFUN_SWAP_FEE_RATIO // Use the same fee ratio as quote
        );

        const outAmount = rawToToken(outputAmountRaw, SOL_DECIMALS); // Convert lamports to SOL
        const price = outAmount / swapRequest.amount; // SOL per token

        swapLogger.info('Bonding curve calculation completed', { 
          input: `${swapRequest.amount} tokens`,
          output: `${outAmount} SOL`,
          price: `${price.toFixed(9)} SOL per token`
        });

        quoteResult = {
          outAmount,
          price,
          outputAmountRaw
        };
        
        transaction = await createCorrectedPumpFunSellTransaction(connection, swapRequest, quoteResult);
      } catch (error: any) {
        swapLogger.error('Error in PumpFun sell calculation', { 
          error: error.message,
          tokenAddress: swapRequest.tokenAddress,
          stack: error.stack
        });
        throw error;
      }
    }

    // Get fresh blockhash
    const { blockhash } = await connection.getLatestBlockhash('finalized');
    transaction.recentBlockhash = blockhash;

    const operationType = swapRequest.direction === SwapDirection.Buy ? 'buy' : 'sell';
    swapLogger.info('Transaction created successfully', {
      instructionCount: transaction.instructions.length,
      operationType,
      feePayer: transaction.feePayer?.toString(),
      recentBlockhash: transaction.recentBlockhash
    });

    // If executeTransaction is false, just return the transaction for external execution (e.g., Jito)
    if (!executeTransaction) {
      const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
      swapLogger.info('Transaction created for external execution (Jito)');
      return { transaction: serializedTransaction, quoteResult };
    }

    // Since PumpFun requires user to be a signer, we need to use Privy session signing
    if (!swapRequest.walletId) {
      const error = new Error('Wallet ID is required for PumpFun transactions with Privy session signing');
      swapLogger.error('Missing wallet ID for Privy signing', { 
        error: error.message,
        walletAddress: swapRequest.walletAddress.substring(0, 8) + '...'
      });
      throw error;
    }

    // Note: Platform doesn't sign the PumpFun transaction as it's not required by the IDL
    // The platform covers gas costs through other means (off-chain reimbursement or separate transaction)
    swapLogger.info('User will sign via Privy, platform covers gas costs through reimbursement');

    try {
      // Import the Privy signing function
      const { signAndSendTransactionWithPrivy } = await import('../privy/proper-privy.service');

      // Serialize transaction for Privy (user will sign, user pays gas but platform reimburses)
      const serializedTransaction = transaction.serialize({ requireAllSignatures: false }).toString('base64');
      swapLogger.debug('Transaction serialized for Privy session signing');

      // Sign and send with Privy (user signs and pays gas, platform reimburses off-chain)
      // Pass connection and priority level for proper confirmation handling
      swapLogger.info('Signing transaction with Privy', {
        walletId: swapRequest.walletId,
        walletAddress: swapRequest.walletAddress.substring(0, 8) + '...',
        priorityLevel: swapRequest.priorityLevel || 'medium'
      });
      
      const priorityLevel = swapRequest.priorityLevel || 'medium';
      const signature = await signAndSendTransactionWithPrivy(
        swapRequest.walletId,
        serializedTransaction,
        connection, // Pass connection for confirmation checking
        priorityLevel,
        true // Wait for confirmation
      );

      swapLogger.info('Transaction signed and sent successfully', { signature });
      return { signature, quoteResult };
    } catch (error: any) {
      swapLogger.error('Error during transaction signing or sending', {
        error: error.message,
        walletAddress: swapRequest.walletAddress.substring(0, 8) + '...',
        stack: error.stack
      });
      throw error;
    }
  } catch (error: any) {
    swapLogger.error('Error in executeCorrectedPumpFunSwap', { 
      error: error.message, 
      direction: swapRequest.direction,
      tokenAddress: swapRequest.tokenAddress,
      stack: error.stack 
    });
    throw error;
  }
}
