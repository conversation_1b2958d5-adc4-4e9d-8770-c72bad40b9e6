// Import dependencies using require to avoid TypeScript esModuleInterop issues
const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log format
const logFormat = winston.format.printf(({ level, message, timestamp, ...meta }: { 
  level: string; 
  message: string; 
  timestamp: string; 
  [key: string]: any 
}) => {
  return `${timestamp} ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
});

// Create specific logger for swap errors
const swapErrorTransport = new winston.transports.File({
  filename: 'logs/swap_error.log',
  level: 'error',
  maxsize: 10485760, // 10MB
  maxFiles: 5,
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.json()
  )
});

// Create logger instance
export const logger = winston.createLogger({
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    logFormat
  ),
  defaultMeta: { service: 'solana-service' },
  transports: [
    // Console transport
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        logFormat
      ),
    }),
    // File transport for errors
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
    }),
    // File transport for all logs
    new winston.transports.File({ 
      filename: 'logs/combined.log',
      maxsize: 10485760, // 10MB
      maxFiles: 5,
    }),
  ],
});

// If not in production, log to console with colors
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    ),
  }));
}

// Create specific loggers for different components
export const swapLogger = {
  info: (message: string, meta?: object) => {
    logger.info(message, { component: 'swap', ...meta });
  },
  warn: (message: string, meta?: object) => {
    logger.warn(message, { component: 'swap', ...meta });
  },
  error: (message: string, meta?: object) => {
    // Log to general error log via logger
    logger.error(message, { component: 'swap', ...meta });
    
    // Also log to swap-specific error log
    const swapErrorLogger = winston.createLogger({
      level: 'error',
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        winston.format.json()
      ),
      defaultMeta: { service: 'solana-service', component: 'swap', ...meta },
      transports: [swapErrorTransport]
    });
    swapErrorLogger.error(message);
  },
  debug: (message: string, meta?: object) => {
    logger.debug(message, { component: 'swap', ...meta });
  }
};

export default logger; 