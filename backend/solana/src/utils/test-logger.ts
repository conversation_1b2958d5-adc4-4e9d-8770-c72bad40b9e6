import { swapLogger } from './logger';

/**
 * Simple utility to test the error logging functionality
 */
export function testSwapErrorLogging(): void {
  try {
    // Simulate a swap error
    throw new Error('This is a test swap error to verify error logging');
  } catch (error: any) {
    // Log the error using swapLogger
    swapLogger.error('Test swap error occurred', { 
      error: error.message, 
      tokenAddress: 'TEST_TOKEN_ADDRESS',
      poolAddress: 'TEST_POOL_ADDRESS',
      stack: error.stack 
    });
    
    console.log('Test error logged successfully. Check logs/swap_error.log and logs/error.log to verify.');
  }
}

// Export a simple function to execute the test
export function runLoggerTest() {
  console.log('Running logger test...');
  testSwapErrorLogging();
  console.log('Logger test completed.');
}

// If this file is run directly, execute the test
if (require.main === module) {
  runLoggerTest();
} 