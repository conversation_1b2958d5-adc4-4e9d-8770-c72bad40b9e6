import { Request, Response } from 'express';
import { Connection, PublicKey } from '@solana/web3.js';
import {
  QuoteRequest,
  QuoteResponse,
  SwapRequest,
  SwapResponse,
  DexType,
  SwapDirection
} from '../../types/pump.types';
import { getSwapQuote, executeSwapWithPrivy } from '../../services/pumpFun/pump.service';
import { checkPumpFunPool } from '../../services/pumpFun/pumpFun.service';
import { config } from '../../config';
import { formatTokenAmount, formatSolAmount, formatPrice } from '../../services/pumpFun/utils.service';
import { jitoService } from '../../services/jito/jito.service';
import { getMEVRecommendation, executeEnhancedSwap } from '../../services/mev/enhanced-swap.service';
import {
  convertSlippageToDecimal,
  convertPriorityFeeToMicroLamports,
  convertBribeAmountToLamports,
  validateSlippage,
  validate<PERSON>riorityFee,
  validateBribeAmount
} from '../../services/common/parameter-converter.service';
import {
  analyzeTokenCharacteristics,
  getNetworkConditions,
  calculateParameterRecommendations
} from '../../services/analysis/token-analysis.service';
import {
  analyzeBondingCurveProgression
} from '../../services/analysis/bonding-curve.service';
import { swapLogger } from '../../utils/logger';

/**
 * Get a quote for a potential swap
 * @param req Express request
 * @param res Express response
 */
export async function getQuote(req: Request, res: Response): Promise<void> {
  try {
    const { tokenAddress, poolAddress, dexType, amount, direction } = req.body;

    // Validate request
    if (!tokenAddress || !poolAddress || !amount || amount <= 0) {
      res.status(400).json({
        success: false,
        error: 'Invalid request parameters. Required: tokenAddress, poolAddress, amount'
      });
      return;
    }

    // Validate dexType
    if (!Object.values(DexType).includes(dexType as DexType)) {
      res.status(400).json({
        success: false,
        error: `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`
      });
      return;
    }

    // Validate direction
    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      res.status(400).json({
        success: false,
        error: `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`
      });
      return;
    }

    const quoteRequest: QuoteRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection
    };

    // Get quote and pool data
    console.log('Getting quote and analyzing token characteristics...');
    const { quoteResult, poolStatus } = await getSwapQuote(quoteRequest);

    // Calculate slippage and fee
    const slippage = config.defaultSlippage;
    const fee = config.platformFeeBasisPoints / 10000;

    // Calculate minimum output with slippage
    const minOut = quoteResult.outAmount * (1 - slippage);

    // Get MEV protection recommendation
    const mevRecommendation = getMEVRecommendation(
      parseFloat(amount),
      slippage
    );

    // Generate intelligent parameter recommendations and bonding curve analysis
    let recommendations;
    let bondingCurveInfo;

    try {
      console.log('Generating intelligent analysis...');

      // Create connection for analysis
      const connection = new Connection(config.rpcUrl || 'https://api.mainnet-beta.solana.com', 'confirmed');
      const tokenMint = new PublicKey(tokenAddress);
      const poolAddress = new PublicKey(quoteRequest.poolAddress);

      // Enhanced analysis for PumpFun tokens
      if (dexType === DexType.PumpFun) {
        console.log('Performing PumpFun-specific analysis...');
        const poolData = await checkPumpFunPool(connection, poolAddress);

        // Analyze bonding curve progression
        console.log('Analyzing bonding curve progression...');
        bondingCurveInfo = analyzeBondingCurveProgression(poolData);
        console.log('✅ Generated bonding curve analysis');

        // Analyze token characteristics for parameter recommendations
        console.log('Analyzing token characteristics...');
        const tokenCharacteristics = await analyzeTokenCharacteristics(connection, tokenMint, poolData);

        // Get current network conditions
        console.log('Analyzing network conditions...');
        const networkConditions = await getNetworkConditions(connection);

        // Calculate intelligent recommendations
        console.log('Calculating parameter recommendations...');
        recommendations = calculateParameterRecommendations(
          tokenCharacteristics,
          networkConditions,
          parseFloat(amount)
        );

        console.log('✅ Generated intelligent parameter recommendations');
      } else {
        console.log('⚠️ Enhanced analysis only available for PumpFun tokens currently');
      }
    } catch (analysisError: any) {
      console.error('Error generating enhanced analysis:', analysisError);
      console.log('Continuing without enhanced analysis...');
      // Continue without enhanced analysis rather than failing the entire quote
    }

    // Debug the quote result before formatting
    console.log('🔍 [CONTROLLER] Quote result before formatting:');
    console.log(`  - outAmount: ${quoteResult.outAmount} (type: ${typeof quoteResult.outAmount})`);
    console.log(`  - price: ${quoteResult.price} (type: ${typeof quoteResult.price})`);
    console.log(`  - outputAmountRaw: ${quoteResult.outputAmountRaw?.toString() || 'undefined'}`);
    console.log(`  - minOut calculated: ${minOut} (type: ${typeof minOut})`);

    // Validate quote result before formatting
    if (quoteResult.outAmount === 0 || quoteResult.outAmount === null || quoteResult.outAmount === undefined) {
      console.error('❌ [CONTROLLER] Invalid outAmount in quote result');
      throw new Error('Quote calculation returned zero or invalid output amount');
    }

    if (quoteResult.price === null || quoteResult.price === undefined || isNaN(quoteResult.price)) {
      console.error('❌ [CONTROLLER] Invalid price in quote result');
      throw new Error('Quote calculation returned invalid price');
    }

    // Format the values for the response
    const formattedOutAmount = direction === SwapDirection.Buy
      ? formatTokenAmount(quoteResult.outAmount)
      : formatSolAmount(quoteResult.outAmount);

    const formattedPrice = formatPrice(quoteResult.price);
    const formattedMinOut = direction === SwapDirection.Buy
      ? formatTokenAmount(minOut)
      : formatSolAmount(minOut);

    console.log('✅ [CONTROLLER] Formatted values:');
    console.log(`  - formattedOutAmount: ${formattedOutAmount}`);
    console.log(`  - formattedPrice: ${formattedPrice}`);
    console.log(`  - formattedMinOut: ${formattedMinOut}`);

    // Enhanced response with MEV recommendations and intelligent parameter suggestions (backward compatible)
    const response: QuoteResponse = {
      success: true,
      data: {
        outAmount: formattedOutAmount,
        price: formattedPrice,
        slippage,
        fee,
        minOut: formattedMinOut,

        // New MEV protection information (optional, doesn't break existing clients)
        mevProtection: {
          recommended: mevRecommendation.recommended,
          tipAmount: mevRecommendation.tipAmount,
          tipAmountSol: mevRecommendation.tipAmount / 1_000_000_000,
          reason: mevRecommendation.reason,
          costPercentage: mevRecommendation.costPercentage,

          // Priority level recommendations
          priorityLevels: {
            low: Math.floor(mevRecommendation.tipAmount * 0.5),
            medium: mevRecommendation.tipAmount,
            high: Math.floor(mevRecommendation.tipAmount * 1.5),
            veryHigh: Math.floor(mevRecommendation.tipAmount * 2.0)
          }
        },

        // New intelligent parameter recommendations (optional, doesn't break existing clients)
        ...(recommendations && {
          recommendations: {
            slippage: recommendations.slippage,
            bribeAmount: recommendations.bribeAmount,
            priorityFee: recommendations.priorityFee,
            reasoning: recommendations.reasoning
          }
        }),

        // New bonding curve information (optional, PumpFun only, doesn't break existing clients)
        ...(bondingCurveInfo && {
          bondingCurve: {
            progressPercentage: bondingCurveInfo.progressPercentage,
            currentSolRaised: bondingCurveInfo.currentSolRaised,
            graduationThreshold: bondingCurveInfo.graduationThreshold,
            isGraduated: bondingCurveInfo.isGraduated,
            status: bondingCurveInfo.status,
            description: bondingCurveInfo.description
          }
        })
      }
    };

    res.status(200).json(response);
  } catch (error) {
    console.error('Error getting quote:', error);
    res.status(500).json({
      success: false,
      error: (error as Error).message || 'An error occurred while getting the quote'
    });
  }
}

/**
 * Execute a swap
 * @param req Express request
 * @param res Express response
 */
export async function executeSwap(req: Request, res: Response): Promise<void> {
  try {
    swapLogger.info('Executing swap request', { 
      tokenAddress: req.body.tokenAddress,
      poolAddress: req.body.poolAddress,
      dexType: req.body.dexType,
      amount: req.body.amount,
      direction: req.body.direction,
      walletAddress: req.body.walletAddress ? req.body.walletAddress.substring(0, 8) + '...' : 'unknown'
    });

    const {
      tokenAddress,
      poolAddress,
      dexType,
      amount,
      direction,
      slippage,
      walletAddress,
      walletId,
      // New MEV protection parameters (optional)
      priorityFee,
      bribeAmount,
      mevProtection,
      priorityLevel
    } = req.body;

    // Validate request
    if (!tokenAddress || !poolAddress || !amount || amount <= 0 || !walletAddress) {
      const error = 'Invalid request parameters. Required: tokenAddress, poolAddress, amount, walletAddress';
      swapLogger.error('Swap validation failed', { error });
      res.status(400).json({
        success: false,
        error
      });
      return;
    }

    // Validate dexType
    if (!Object.values(DexType).includes(dexType as DexType)) {
      const error = `Invalid dexType. Must be one of: ${Object.values(DexType).join(', ')}`;
      swapLogger.error('Swap validation failed', { error, receivedDexType: dexType });
      res.status(400).json({
        success: false,
        error
      });
      return;
    }

    // Validate direction
    if (!Object.values(SwapDirection).includes(direction as SwapDirection)) {
      const error = `Invalid direction. Must be one of: ${Object.values(SwapDirection).join(', ')}`;
      swapLogger.error('Swap validation failed', { error, receivedDirection: direction });
      res.status(400).json({
        success: false,
        error
      });
      return;
    }

    // Enhanced parameter validation and conversion
    const tradeValueSol = parseFloat(amount);

    // Validate and convert slippage (supports both percentage and decimal formats)
    const inputSlippage = slippage ? parseFloat(slippage) : config.defaultSlippage * 100; // Convert default to percentage
    const slippageValidation = validateSlippage(inputSlippage);
    if (!slippageValidation.isValid) {
      const error = `Invalid slippage: ${slippageValidation.error}`;
      swapLogger.error('Swap validation failed', { error, receivedSlippage: slippage });
      res.status(400).json({
        success: false,
        error
      });
      return;
    }
    const slippageValue = convertSlippageToDecimal(inputSlippage);

    // Validate and convert priority fee (supports both percentage and microLamports)
    let finalPriorityFee = 200000; // Default 200,000 microLamports
    if (priorityFee !== undefined) {
      const priorityFeeValidation = validatePriorityFee(priorityFee, tradeValueSol);
      if (!priorityFeeValidation.isValid) {
        const error = `Invalid priorityFee: ${priorityFeeValidation.error}`;
        swapLogger.error('Swap validation failed', { error, receivedPriorityFee: priorityFee });
        res.status(400).json({
          success: false,
          error
        });
        return;
      }
      finalPriorityFee = convertPriorityFeeToMicroLamports(priorityFee, tradeValueSol);
    }

    // Validate and convert bribe amount (supports both percentage and lamports)
    let finalBribeAmountInput = bribeAmount;
    if (bribeAmount !== undefined) {
      const bribeValidation = validateBribeAmount(bribeAmount, tradeValueSol);
      if (!bribeValidation.isValid) {
        const error = `Invalid bribeAmount: ${bribeValidation.error}`;
        swapLogger.error('Swap validation failed', { error, receivedBribeAmount: bribeAmount });
        res.status(400).json({
          success: false,
          error
        });
        return;
      }
    }

    if (priorityLevel && !['low', 'medium', 'high', 'veryHigh'].includes(priorityLevel)) {
      const error = 'Invalid priorityLevel. Must be one of: low, medium, high, veryHigh';
      swapLogger.error('Swap validation failed', { error, receivedPriorityLevel: priorityLevel });
      res.status(400).json({
        success: false,
        error
      });
      return;
    }

    // Determine MEV protection based on priorityLevel (updated logic)
    const finalPriorityLevel = priorityLevel || 'medium';

    // FIXED: Allow MEV protection for all priority levels, not just high/veryHigh
    // This ensures low priority transactions can also benefit from MEV protection
    const shouldUseMEVFromPriority = jitoService.isMEVProtectionRecommended(tradeValueSol, slippageValue);

    // Calculate intelligent defaults for MEV parameters
    const shouldUseMEV = mevProtection !== undefined
      ? mevProtection === true || mevProtection === 'true'  // Explicit user choice takes precedence
      : shouldUseMEVFromPriority; // Use recommendation based on trade value and slippage, not priority level

    // Convert bribe amount using the new conversion logic
    let finalBribeAmount = 0;
    if (finalBribeAmountInput !== undefined) {
      finalBribeAmount = convertBribeAmountToLamports(finalBribeAmountInput, tradeValueSol);
    } else if (shouldUseMEV) {
      // Use intelligent default based on priority level
      const baseTipAmount = jitoService.calculateRecommendedTip(tradeValueSol);
      const priorityMultipliers = {
        low: 0.5,
        medium: 1.0,
        high: 1.5,
        veryHigh: 2.0
      };
      const multiplier = priorityMultipliers[finalPriorityLevel as keyof typeof priorityMultipliers] || 1.0;
      finalBribeAmount = Math.floor(baseTipAmount * multiplier);
    }

    swapLogger.info('Enhanced parameter conversion completed', {
      inputSlippage: slippage,
      convertedSlippage: `${slippageValue} (${(slippageValue * 100).toFixed(2)}%)`,
      inputPriorityFee: priorityFee,
      convertedPriorityFee: `${finalPriorityFee} microLamports`,
      inputBribeAmount: bribeAmount,
      convertedBribeAmount: `${finalBribeAmount} lamports (${(finalBribeAmount / 1_000_000_000).toFixed(6)} SOL)`
    });

    swapLogger.info('MEV protection settings', {
      priorityLevel: finalPriorityLevel,
      mevRecommended: shouldUseMEVFromPriority,
      requestedMevProtection: mevProtection,
      tradeValue: `${tradeValueSol} SOL`,
      slippage: `${(slippageValue * 100).toFixed(2)}%`,
      finalMevProtection: shouldUseMEV,
      finalBribeAmount: `${finalBribeAmount} lamports (${(finalBribeAmount / 1_000_000_000).toFixed(6)} SOL)`
    });

    const swapRequest: SwapRequest = {
      tokenAddress,
      poolAddress,
      dexType: dexType as DexType,
      amount: parseFloat(amount),
      direction: direction as SwapDirection,
      slippage: slippageValue,
      walletAddress,
      walletId, // Include walletId in the swap request

      // Include MEV protection parameters (converted from user-friendly formats)
      priorityFee: finalPriorityFee,
      bribeAmount: finalBribeAmount,
      mevProtection: shouldUseMEV,
      priorityLevel: finalPriorityLevel
    };

    swapLogger.info('Creating swap transaction', { 
      tokenAddress,
      poolAddress,
      dexType,
      amount: parseFloat(amount),
      direction,
      slippageValue,
      walletAddress: walletAddress.substring(0, 8) + '...',
      mevProtection: shouldUseMEV,
      priorityLevel: finalPriorityLevel
    });

    try {
      // Choose execution method based on MEV protection settings
      let result: any;
      let executionMethod: string;

      // Force regular Privy execution when mevProtection is explicitly false
      if (mevProtection === false) {
        swapLogger.info('MEV Protection explicitly disabled - using regular Privy execution');
        result = await executeSwapWithPrivy(swapRequest, true, walletId);
        executionMethod = 'privy';
      } else if (shouldUseMEV && finalBribeAmount > 0) {
        // Use enhanced swap with MEV protection (available for all priority levels)
        swapLogger.info(`MEV Protection enabled (${finalPriorityLevel} priority) - using Jito execution`);
        result = await executeEnhancedSwap(swapRequest);
        executionMethod = result.executionMethod || 'jito';
      } else {
        // Use regular Privy execution (MEV not recommended or no bribe amount)
        swapLogger.info(`MEV Protection not recommended - using regular Privy execution (${finalPriorityLevel} priority)`);
        result = await executeSwapWithPrivy(swapRequest, true, walletId);
        executionMethod = 'privy';
      }

      swapLogger.info('Swap executed successfully', {
        executionMethod,
        signature: result.signature || result.bundleId,
        outputAmount: direction === SwapDirection.Buy ? 
          result.quoteResult.outAmount + ' tokens' : 
          result.quoteResult.outAmount + ' SOL'
      });

      // Format the values for the response
      const formattedOutAmount = direction === SwapDirection.Buy
        ? formatTokenAmount(result.quoteResult.outAmount)
        : formatSolAmount(result.quoteResult.outAmount);

      const formattedPrice = formatPrice(result.quoteResult.price);

      // Create enhanced response with MEV protection details
      let response: SwapResponse;

      if (result.signature || result.bundleId) {
        // Transaction was executed (either Privy or Jito)
        const solscanUrl = result.signature ? `https://solscan.io/tx/${result.signature}` : undefined;
        const jitoUrl = result.bundleId ? `https://explorer.jito.wtf/bundle/${result.bundleId}` : undefined;

        response = {
          success: true,
          data: {
            outAmount: formattedOutAmount,
            price: formattedPrice,
            signature: result.signature || null,
            bundleId: result.bundleId || null,
            solscanUrl,
            jitoUrl,
            executionMethod: executionMethod as 'privy' | 'jito' | 'regular' | 'client-side',
            mevProtected: shouldUseMEV,
            tipAmount: finalBribeAmount,
            tipAmountSol: finalBribeAmount / 1_000_000_000
          }
        };
      } else {
        // Transaction was created but not executed
        response = {
          success: true,
          data: {
            outAmount: formattedOutAmount,
            price: formattedPrice,
            transaction: result.transaction,
            executionMethod: 'client-side' as 'client-side',
            mevProtected: shouldUseMEV,
            tipAmount: 0,
            tipAmountSol: 0
          }
        };
      }

      res.status(200).json(response);
    } catch (innerError: any) {
      // Handle execution errors
      swapLogger.error('Swap execution failed', {
        error: innerError.message,
        stack: innerError.stack,
        tokenAddress,
        dexType,
        direction
      });
      
      res.status(500).json({
        success: false,
        error: innerError.message || 'An error occurred while executing the swap',
      });
    }
  } catch (error: any) {
    // Handle overall errors
    swapLogger.error('Unhandled error in swap controller', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      error: error.message || 'An unexpected error occurred',
    });
  }
}
