import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { createServer } from 'http';
import config from './config/index.js';
// Load environment variables
dotenv.config();
import homeRoutes from './routes/homeRoutes.js';
import walletRoutes from './routes/walletRoutes.js';
import activityRoutes from './routes/activityRoutes.js';
import limitOrderRoutes from './routes/limitOrderRoutes.js';
import { errorHandler, notFound } from './middleware/errorHandler.js';
import { initRedis } from './utils/redis.js';
import { initializeCaches } from './utils/cacheInitializer.js';
import { scheduleCacheRefresh } from './utils/cacheScheduler.js';
import { logger } from './utils/logger.js';
import { frontendWebSocketService } from './services/frontendWebSocketService.js';
import { workerThreadService } from './services/workerThreadService.js';
import { performanceMonitorService } from './services/performanceMonitorService.js';
import { limitOrderMonitoringService } from './services/limitOrderMonitoringService.js';
// Initialize express app
const app = express();
// Middleware
app.use(cors({
    origin: '*', // Allow all origins
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
// Add performance monitoring middleware
app.use(performanceMonitorService.createMiddleware());
// Routes
app.get('/', (req, res) => {
    res.json({ message: 'Welcome to the Spot Trading API' });
});
// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        service: 'spot-backend',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});
// Enhanced performance monitoring endpoint
app.get('/api/performance', (req, res) => {
    try {
        const performanceStats = performanceMonitorService.getStats();
        const realTimeMetrics = performanceMonitorService.getRealTimeMetrics();
        const workerStats = workerThreadService.getStats();
        res.json({
            status: 'success',
            timestamp: Date.now(),
            data: {
                performance: performanceStats,
                workers: workerStats,
                realTime: realTimeMetrics,
                cache: {},
                deduplication: {},
                memory: {
                    heapUsed: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
                    heapTotal: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
                    external: Math.round(process.memoryUsage().external / 1024 / 1024),
                    rss: Math.round(process.memoryUsage().rss / 1024 / 1024)
                },
                uptime: Math.round(process.uptime())
            }
        });
    }
    catch (error) {
        logger.error('Performance endpoint error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Failed to retrieve performance metrics'
        });
    }
});
// WebSocket health check endpoint
app.get('/api/websocket/health', (req, res) => {
    try {
        const isInitialized = frontendWebSocketService.isInitialized();
        const stats = frontendWebSocketService.getStats();
        if (isInitialized) {
            res.json({
                ready: true,
                status: 'active',
                connectedClients: stats.totalClients,
                pulseRoomClients: stats.pulseRoomClients,
                message: 'WebSocket service is running',
                timestamp: Date.now()
            });
        }
        else {
            res.status(503).json({
                ready: false,
                status: 'initializing',
                connectedClients: 0,
                pulseRoomClients: 0,
                message: 'WebSocket service is initializing',
                timestamp: Date.now()
            });
        }
    }
    catch (error) {
        res.status(500).json({
            ready: false,
            status: 'error',
            connectedClients: 0,
            pulseRoomClients: 0,
            message: `WebSocket service error: ${error.message}`,
            timestamp: Date.now()
        });
    }
});
// Limit Order Monitoring health check endpoint
app.get('/api/limit-orders/monitoring/health', async (req, res) => {
    try {
        const status = limitOrderMonitoringService.getStatus();
        const stats = limitOrderMonitoringService.getStats();
        res.json({
            connected: status.connected,
            activeSubscriptions: status.activeSubscriptions,
            totalOrders: status.totalOrders,
            lastUpdate: status.lastUpdate,
            connectionState: status.connectionState,
            config: status.config,
            stats: stats,
            message: status.connected ? 'Monitoring service active' : 'Monitoring service disconnected',
            timestamp: Date.now()
        });
    }
    catch (error) {
        res.status(500).json({
            error: 'Failed to get Limit Order Monitoring status',
            message: error.message,
            timestamp: Date.now()
        });
    }
});
// Limit Order Monitoring subscriptions endpoint (for debugging)
app.get('/api/limit-orders/monitoring/subscriptions', async (req, res) => {
    try {
        // const subscriptions = limitOrderMonitoringService.getSubscriptions();
        res.json({
            success: false,
            data: [],
            count: 0,
            message: 'Monitoring service temporarily disabled',
            timestamp: Date.now()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: 'Failed to get monitoring subscriptions',
            message: error.message,
            timestamp: Date.now()
        });
    }
});
// Manual refresh endpoint (for testing/debugging)
app.post('/api/limit-orders/monitoring/refresh', async (req, res) => {
    try {
        await limitOrderMonitoringService.refreshOrders();
        res.json({
            success: true,
            message: 'Orders refreshed successfully',
            timestamp: Date.now()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: 'Failed to refresh orders',
            message: error.message,
            timestamp: Date.now()
        });
    }
});
// Limit Order Monitoring Status endpoint
app.get('/api/limit-orders/monitoring/status', async (req, res) => {
    try {
        const status = limitOrderMonitoringService.getStatus();
        const stats = limitOrderMonitoringService.getStats();
        res.json({
            success: true,
            status,
            stats,
            timestamp: Date.now()
        });
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: 'Failed to get monitoring status',
            message: error.message,
            timestamp: Date.now()
        });
    }
});
// API Routes
app.use('/api/home', homeRoutes);
app.use('/api/wallet', walletRoutes);
// Also register wallet routes without /api prefix for backward compatibility
app.use('/wallet', walletRoutes);
app.use('/api/activity', activityRoutes);
app.use('/api/limit-orders', limitOrderRoutes);
// Error handling middleware
app.use(notFound);
app.use(errorHandler);
// Create HTTP server
const httpServer = createServer(app);
// Start the server
const PORT = config.port;
const server = httpServer.listen(Number(PORT), '0.0.0.0', async () => {
    console.log(`Server running on port ${PORT}`);
    try {
        // Initialize Redis first
        await initRedis();
        // Test database connection
        console.log('🗄️ Database connection test skipped (will test via API)');
        // const dbConnected = await testDatabaseConnection();
        // if (dbConnected) {
        //   console.log('✅ Database connection successful');
        // } else {
        //   console.warn('⚠️ Database connection failed - limit orders may not work');
        // }
        // Initialize Frontend WebSocket service EARLY - before heavy operations
        console.log('🔧 Initializing Frontend WebSocket service...');
        try {
            frontendWebSocketService.initialize(httpServer);
            console.log('✅ Frontend WebSocket service initialization completed');
        }
        catch (error) {
            console.error('❌ Failed to initialize Frontend WebSocket service:', error.message);
            logger.error('Frontend WebSocket service initialization failed:', error);
        }
        // Initialize Worker Thread Service
        console.log('🧵 Initializing Worker Thread Service...');
        try {
            await workerThreadService.initialize();
            console.log('✅ Worker Thread Service initialization completed');
        }
        catch (error) {
            console.error('❌ Failed to initialize Worker Thread Service:', error.message);
            logger.error('Worker Thread Service initialization failed:', error);
            // Continue without worker threads - will fall back to main thread processing
        }
        // Initialize Limit Order Monitoring Service
        console.log('🔍 Initializing Limit Order Monitoring Service...');
        try {
            await limitOrderMonitoringService.initialize();
            console.log('✅ Limit Order Monitoring Service initialization completed');
        }
        catch (error) {
            console.error('❌ Failed to initialize Limit Order Monitoring Service:', error.message);
            logger.error('Limit Order Monitoring Service initialization failed:', error);
            // Continue without monitoring service to prevent startup failure
        }
        // Initialize all data caches (this can take time)
        console.log('📦 Initializing data caches...');
        await initializeCaches();
        console.log('✅ Data caches initialization completed');
        // Set up scheduled tasks with worker thread support
        scheduleCacheRefresh('*/5 * * * *');
        logger.info('Server initialization completed');
        // Return realistic values - this is what we'll show to users
        // In production, this would be replaced with real blockchain data
        const balances = {
            BTC: 0.128, // ~$5,500 at current prices
            ETH: 0.25, // ~$500 at current prices
            SOL: 1.5 // ~$150 at current prices
        };
    }
    catch (error) {
        logger.error('Error during server initialization:', error);
    }
});
// Graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('SIGTERM received, shutting down gracefully');
    // Shutdown services in order
    try {
        await limitOrderMonitoringService.shutdown();
        await workerThreadService.shutdown();
        frontendWebSocketService.shutdown();
        performanceMonitorService.stop();
    }
    catch (error) {
        logger.error('Error during service shutdown:', error);
    }
    server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', async () => {
    logger.info('SIGINT received, shutting down gracefully');
    // Shutdown services in order
    // await limitOrderMonitoringService.shutdown();
    // await workerThreadService.shutdown();
    frontendWebSocketService.shutdown();
    // performanceMonitorService.stop();
    server.close(() => {
        logger.info('Process terminated');
        process.exit(0);
    });
});
//# sourceMappingURL=index.js.map