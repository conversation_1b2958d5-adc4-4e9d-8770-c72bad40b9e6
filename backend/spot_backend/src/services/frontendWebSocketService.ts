import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { logger } from '../utils/logger.js';
import { mobulaWebSocketService } from './mobulaWebSocketService.js';
import { userActivityService } from './userActivityService.js';


interface ConnectedClient {
  id: string;
  userId: string;
  sessionId: string;
  connectedAt: number;
  lastActivity: number;
  isOnPulsePage: boolean;
}

interface PulseDataUpdate {
  data: any;
  timestamp: number;
  source: 'websocket' | 'api';
}





class FrontendWebSocketService {
  private io: SocketIOServer | null = null;
  private connectedClients = new Map<string, ConnectedClient>();
  private pulseRoomClients = new Set<string>();

  private heartbeatInterval: NodeJS.Timeout | null = null;
  private connectionPool = new Map<string, { socket: any; lastUsed: number }>();
  private readonly maxPoolSize = 100;
  private readonly poolCleanupInterval = 300000; // 5 minutes
  private pulseDataInterval: NodeJS.Timeout | null = null; // Track pulse data polling timer
  private timers: Set<NodeJS.Timeout> = new Set(); // Track all timers for cleanup
  private initialized = false;

  // Enhanced connection lifecycle management
  private clientActivityTimeout: NodeJS.Timeout | null = null;
  private readonly CLIENT_INACTIVITY_TIMEOUT = 30000; // 30 seconds
  private lastClientActivity = Date.now();



  /**
   * Initialize the WebSocket server
   */
  public initialize(httpServer: HttpServer): void {
    if (this.initialized) {
      logger.warn('Frontend WebSocket service already initialized');
      return;
    }

    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: [
          "http://localhost:4001",
          "http://127.0.0.1:4001",
          "http://localhost:5173",
          "http://127.0.0.1:5173",
          "https://redfyn.crypfi.io",
          "https://redfyn.lrbinfotech.com"
        ],
        methods: ["GET", "POST"],
        credentials: false
      },
      transports: ['websocket', 'polling'],
      pingTimeout: 60000,
      pingInterval: 25000,
      allowEIO3: true
    });

    this.setupEventHandlers();
    this.startHeartbeat();
    this.setupMobulaDataListener();
    this.initialized = true;

    logger.info('✅ Frontend WebSocket server initialized successfully');
  }

  /**
   * Setup Socket.IO event handlers
   */
  private setupEventHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      logger.info(`🔌 Frontend client connected: ${socket.id}`);

      // Handle client registration
      socket.on('register', (data: { userId: string; sessionId: string }) => {
        this.handleClientRegistration(socket, data);
      });

      // Handle pulse page join
      socket.on('join-pulse', (data: { userId: string; sessionId: string }) => {
        this.handlePulsePageJoin(socket, data);
      });

      // Handle pulse page leave
      socket.on('leave-pulse', (data: { userId: string; sessionId: string }) => {
        this.handlePulsePageLeave(socket, data);
      });



      // Handle heartbeat
      socket.on('heartbeat', (data: { userId: string; sessionId: string }) => {
        this.handleHeartbeat(socket, data);
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        this.handleClientDisconnection(socket, reason);
      });

      // Handle errors
      socket.on('error', (error) => {
        logger.error(`❌ Socket error for client ${socket.id}:`, error);
      });
    });
  }

  /**
   * Handle client registration
   */
  private handleClientRegistration(socket: any, data: { userId: string; sessionId: string }): void {
    const { userId, sessionId } = data;
    
    if (!userId || !sessionId) {
      socket.emit('error', { message: 'userId and sessionId are required' });
      return;
    }

    const client: ConnectedClient = {
      id: socket.id,
      userId,
      sessionId,
      connectedAt: Date.now(),
      lastActivity: Date.now(),
      isOnPulsePage: false
    };

    this.connectedClients.set(socket.id, client);
    
    // Send current connection status
    socket.emit('connection-status', {
      connected: true,
      clientId: socket.id,
      timestamp: Date.now()
    });

    logger.info(`📝 Client registered: ${socket.id} (User: ${userId})`);
  }

  /**
   * Handle pulse page join
   */
  private handlePulsePageJoin(socket: any, data: { userId: string; sessionId: string }): void {
    const { userId, sessionId } = data;
    const client = this.connectedClients.get(socket.id);

    if (!client) {
      socket.emit('error', { message: 'Client not registered' });
      return;
    }

    // Join pulse room
    socket.join('pulse-room');
    this.pulseRoomClients.add(socket.id);
    client.isOnPulsePage = true;
    client.lastActivity = Date.now();

    // Update global client activity tracking
    this.updateClientActivity();

    // Register with user activity service
    userActivityService.registerPulseActivity(userId, sessionId);

    // Send current pulse data with improved fallback handling
    const cachedData = mobulaWebSocketService.getCachedData();
    const hasFreshData = mobulaWebSocketService.hasFreshData();
    const hasUsableData = mobulaWebSocketService.hasUsableData();

    if (hasUsableData) {
      // Determine data source and freshness
      let dataSource: 'websocket' | 'api' | 'fallback' = 'api';
      let dataAge = 0;

      if (hasFreshData) {
        dataSource = 'websocket';
      } else if (cachedData) {
        dataSource = 'fallback';
        // Calculate approximate age for logging
        const status = mobulaWebSocketService.getStatus();
        if (status.lastUpdate) {
          dataAge = Date.now() - status.lastUpdate;
        }
      }

      const pulseUpdate: PulseDataUpdate = {
        data: cachedData,
        timestamp: Date.now(),
        source: dataSource as 'websocket' | 'api'
      };

      socket.emit('pulse-data', pulseUpdate);

      if (dataSource === 'fallback') {
        logger.info(`📤 Sent fallback pulse data to client ${socket.id} (age: ${Math.round(dataAge / 1000)}s) - fresh data will follow`);
      } else {
        logger.info(`📤 Sent initial pulse data to client ${socket.id} (source: ${dataSource}, fresh: ${hasFreshData})`);
      }
    } else {
      // No data available at all - send empty structure
      const emptyPulseUpdate: PulseDataUpdate = {
        data: { new: [], bonding: [], bonded: [] },
        timestamp: Date.now(),
        source: 'api'
      };

      socket.emit('pulse-data', emptyPulseUpdate);
      logger.info(`📤 Sent empty pulse data to client ${socket.id} - no cached data available`);
    }

    // Immediately ensure we have fresh data coming by checking connection status
    logger.info(`🎯 Client joined pulse room: ${socket.id} (User: ${userId}) - ensuring fresh data flow`);
    this.ensureFreshDataFlow();

    // Also trigger an immediate check for new data
    setTimeout(() => {
      this.checkForNewPulseData();
    }, 1000); // Check after 1 second to allow connection to establish

    // Emit room stats to all pulse clients
    this.broadcastPulseRoomStats();
  }

  /**
   * Handle pulse page leave
   */
  private handlePulsePageLeave(socket: any, data: { userId: string; sessionId: string }): void {
    const { userId, sessionId } = data;
    const client = this.connectedClients.get(socket.id);

    if (!client) return;

    // Leave pulse room
    socket.leave('pulse-room');
    this.pulseRoomClients.delete(socket.id);
    client.isOnPulsePage = false;
    client.lastActivity = Date.now();

    // Update global client activity tracking
    this.updateClientActivity();

    // Unregister from user activity service
    userActivityService.unregisterPulseActivity(userId, sessionId);

    logger.info(`🚪 Client left pulse room: ${socket.id} (User: ${userId})`);

    // Check if we should trigger backend cleanup
    this.checkBackendConnectionCleanup();

    // Restore normal polling frequency if no clients remain
    if (this.pulseRoomClients.size === 0 && this.pulseDataInterval) {
      clearInterval(this.pulseDataInterval);
      this.timers.delete(this.pulseDataInterval);

      this.pulseDataInterval = setInterval(() => {
        this.checkForNewPulseData();
      }, 15000); // Back to 15 seconds when no clients

      this.timers.add(this.pulseDataInterval);
      logger.debug('🔄 Restored normal polling frequency - no active clients');
    }

    // Emit room stats to remaining pulse clients
    this.broadcastPulseRoomStats();
  }





  /**
   * Handle client heartbeat
   */
  private handleHeartbeat(socket: any, data: { userId: string; sessionId: string }): void {
    const client = this.connectedClients.get(socket.id);
    
    if (client) {
      client.lastActivity = Date.now();
      userActivityService.updateActivity(data.userId, data.sessionId);
      
      socket.emit('heartbeat-ack', { timestamp: Date.now() });
      logger.debug(`💓 Heartbeat received from client: ${socket.id}`);
    }
  }

  /**
   * Handle client disconnection
   */
  private handleClientDisconnection(socket: any, reason: string): void {
    const client = this.connectedClients.get(socket.id);

    if (client) {
      // Clean up pulse room if client was in it
      if (client.isOnPulsePage) {
        this.pulseRoomClients.delete(socket.id);
        userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
      }

      this.connectedClients.delete(socket.id);

      // Update global client activity tracking
      this.updateClientActivity();

      logger.info(`🔌 Client disconnected: ${socket.id} (User: ${client.userId}, Reason: ${reason})`);

      // Check if we should trigger backend cleanup
      this.checkBackendConnectionCleanup();

      // Emit room stats to remaining pulse clients
      this.broadcastPulseRoomStats();
    }
  }

  /**
   * Update client activity tracking for backend connection management
   */
  private updateClientActivity(): void {
    this.lastClientActivity = Date.now();

    // Clear existing timeout
    if (this.clientActivityTimeout) {
      clearTimeout(this.clientActivityTimeout);
      this.clientActivityTimeout = null;
    }

    // If we have active clients, don't schedule cleanup
    if (this.pulseRoomClients.size > 0) {
      logger.debug(`📊 Client activity updated: ${this.pulseRoomClients.size} active pulse clients`);
      return;
    }

    // Schedule backend cleanup check if no active clients
    this.clientActivityTimeout = setTimeout(() => {
      this.checkBackendConnectionCleanup();
    }, this.CLIENT_INACTIVITY_TIMEOUT);

    logger.debug(`⏰ Scheduled backend cleanup check in ${this.CLIENT_INACTIVITY_TIMEOUT / 1000}s`);
  }

  /**
   * Check if backend connections should be cleaned up due to client inactivity
   */
  private checkBackendConnectionCleanup(): void {
    const hasActivePulseClients = this.pulseRoomClients.size > 0;
    const timeSinceLastActivity = Date.now() - this.lastClientActivity;

    logger.info(`🔍 Backend cleanup check:`, {
      activePulseClients: this.pulseRoomClients.size,
      totalClients: this.connectedClients.size,
      timeSinceLastActivity: Math.round(timeSinceLastActivity / 1000),
      threshold: Math.round(this.CLIENT_INACTIVITY_TIMEOUT / 1000)
    });

    // If no active pulse clients and enough time has passed, trigger backend cleanup
    if (!hasActivePulseClients && timeSinceLastActivity >= this.CLIENT_INACTIVITY_TIMEOUT) {
      logger.info('🧹 Triggering backend WebSocket cleanup - no active pulse clients');
      this.triggerBackendCleanup();
    }
  }

  /**
   * Trigger cleanup of backend WebSocket connections
   */
  private triggerBackendCleanup(): void {
    try {
      // Unregister all users from Mobula WebSocket service
      const mobulaStatus = mobulaWebSocketService.getStatus();

      if (mobulaStatus.activeUsers > 0) {
        logger.info(`🧹 Cleaning up ${mobulaStatus.activeUsers} active users from Mobula WebSocket service`);

        // Force cleanup by resetting the service state
        mobulaWebSocketService.resetServiceState();

        logger.info('✅ Backend WebSocket cleanup completed');
      } else {
        logger.debug('🧹 No active users in Mobula WebSocket service - cleanup not needed');
      }
    } catch (error: any) {
      logger.error('❌ Error during backend cleanup:', error.message);
    }
  }



  /**
   * Setup listener for Mobula WebSocket data updates
   */
  private setupMobulaDataListener(): void {
    // We'll extend the MobulaWebSocketService to emit events when new data arrives
    // For now, we'll poll for new data periodically with reduced frequency
    this.pulseDataInterval = setInterval(() => {
      this.checkForNewPulseData();
    }, 15000); // Check every 15 seconds (reduced from 5 seconds)

    // Track timer for cleanup
    this.timers.add(this.pulseDataInterval);


  }





  /**
   * Check for new pulse data and broadcast to clients
   */
  private checkForNewPulseData(): void {
    if (this.pulseRoomClients.size === 0) {
      return; // No clients in pulse room
    }

    const cachedData = mobulaWebSocketService.getCachedData();
    if (cachedData && mobulaWebSocketService.hasFreshData()) {
      const pulseUpdate: PulseDataUpdate = {
        data: cachedData,
        timestamp: Date.now(),
        source: 'websocket'
      };

      this.broadcastToPulseRoom('pulse-data', pulseUpdate);
      logger.debug(`📡 Broadcasted pulse data to ${this.pulseRoomClients.size} clients`);
    } else if (this.pulseRoomClients.size > 0) {
      // We have clients but no fresh data - ensure connection is active
      this.ensureFreshDataFlow();
    }
  }

  /**
   * Ensure fresh data flow is active when clients are connected
   */
  private ensureFreshDataFlow(): void {
    const status = mobulaWebSocketService.getStatus();
    const clientCount = this.pulseRoomClients.size;

    logger.info(`🔄 Ensuring fresh data flow: ${clientCount} clients, Mobula connected: ${status.connected}`);

    if (clientCount > 0) {
      if (!status.connected) {
        logger.info('🔄 Clients waiting for data but Mobula WebSocket not connected - triggering immediate reconnection');

        // Force reconnection by registering a temporary user
        const tempUserId = `frontend-service-${Date.now()}`;
        mobulaWebSocketService.registerUser(tempUserId);

        // Remove the temporary user after a short delay to allow connection
        setTimeout(() => {
          mobulaWebSocketService.unregisterUser(tempUserId);
          logger.info('🔄 Removed temporary user after connection attempt');
        }, 5000);
      } else {
        logger.info('🔄 Mobula WebSocket already connected, checking data freshness');
        // Even if connected, check if we have recent data
        const lastUpdate = status.lastUpdate;
        const dataAge = lastUpdate ? Date.now() - lastUpdate : Infinity;

        if (dataAge > 60000) { // If data is older than 1 minute
          logger.info(`🔄 Data is stale (${Math.round(dataAge / 1000)}s old), requesting fresh data`);
          // Request fresh data by briefly registering a user
          const refreshUserId = `refresh-${Date.now()}`;
          mobulaWebSocketService.registerUser(refreshUserId);
          setTimeout(() => {
            mobulaWebSocketService.unregisterUser(refreshUserId);
          }, 2000);
        }
      }

      // Increase polling frequency when clients are active
      if (this.pulseDataInterval) {
        clearInterval(this.pulseDataInterval);
        this.timers.delete(this.pulseDataInterval);
      }

      this.pulseDataInterval = setInterval(() => {
        this.checkForNewPulseData();
      }, 5000); // Check every 5 seconds when clients are active

      this.timers.add(this.pulseDataInterval);
      logger.debug('🔄 Set active polling frequency (5s) for pulse data');
    }
  }

  /**
   * Broadcast pulse room statistics
   */
  private broadcastPulseRoomStats(): void {
    const stats = {
      totalClients: this.connectedClients.size,
      pulseRoomClients: this.pulseRoomClients.size,
      timestamp: Date.now()
    };

    this.broadcastToPulseRoom('room-stats', stats);
  }

  /**
   * Broadcast message to all clients in pulse room
   */
  private broadcastToPulseRoom(event: string, data: any): void {
    if (!this.io) return;

    this.io.to('pulse-room').emit(event, data);
  }



  /**
   * Start heartbeat monitoring for connected clients
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.cleanupInactiveClients();
    }, 30000); // Check every 30 seconds

    // Track timer for cleanup
    this.timers.add(this.heartbeatInterval);

    logger.info('💓 Started frontend client heartbeat monitoring');
  }

  /**
   * Clean up inactive clients
   */
  private cleanupInactiveClients(): void {
    const now = Date.now();
    const inactiveThreshold = 5 * 60 * 1000; // 5 minutes
    const clientsToRemove: string[] = [];

    for (const [socketId, client] of this.connectedClients.entries()) {
      if (now - client.lastActivity > inactiveThreshold) {
        clientsToRemove.push(socketId);
      }
    }

    clientsToRemove.forEach(socketId => {
      const client = this.connectedClients.get(socketId);
      if (client) {
        logger.info(`🧹 Cleaning up inactive client: ${socketId} (User: ${client.userId})`);

        if (client.isOnPulsePage) {
          this.pulseRoomClients.delete(socketId);
          userActivityService.unregisterPulseActivity(client.userId, client.sessionId);
        }

        this.connectedClients.delete(socketId);

        // Disconnect the socket if still connected
        const socket = this.io?.sockets.sockets.get(socketId);
        if (socket) {
          socket.disconnect(true);
        }
      }
    });

    if (clientsToRemove.length > 0) {
      this.broadcastPulseRoomStats();
    }
  }

  /**
   * Manually broadcast new pulse data (called by MobulaWebSocketService)
   */
  public broadcastPulseData(data: any, source: 'websocket' | 'api' = 'websocket'): void {
    if (this.pulseRoomClients.size === 0) {
      return; // No clients to broadcast to
    }

    const pulseUpdate: PulseDataUpdate = {
      data,
      timestamp: Date.now(),
      source
    };

    this.broadcastToPulseRoom('pulse-data', pulseUpdate);
    logger.info(`📡 Broadcasted new pulse data to ${this.pulseRoomClients.size} clients (source: ${source})`);
  }





  /**
   * Check if the service is initialized
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Get service status for health checks
   */
  public getStatus(): {
    initialized: boolean;
    totalClients: number;
    pulseRoomClients: number;
    hasActiveConnections: boolean;
    timestamp: number;
  } {
    return {
      initialized: this.initialized,
      totalClients: this.connectedClients.size,
      pulseRoomClients: this.pulseRoomClients.size,
      hasActiveConnections: this.connectedClients.size > 0,
      timestamp: Date.now()
    };
  }

  /**
   * Get service statistics
   */
  public getStats(): {
    totalClients: number;
    pulseRoomClients: number;
    isInitialized: boolean;
    connectedClients: Array<{
      id: string;
      userId: string;
      connectedAt: number;
      lastActivity: number;
      isOnPulsePage: boolean;
    }>;
  } {
    return {
      totalClients: this.connectedClients.size,
      pulseRoomClients: this.pulseRoomClients.size,
      isInitialized: this.initialized,
      connectedClients: Array.from(this.connectedClients.values()).map(client => ({
        id: client.id,
        userId: client.userId,
        connectedAt: client.connectedAt,
        lastActivity: client.lastActivity,
        isOnPulsePage: client.isOnPulsePage
      }))
    };
  }

  /**
   * Shutdown the service gracefully with comprehensive timer cleanup
   */
  public shutdown(): void {
    logger.info('🛑 Shutting down Frontend WebSocket service...');

    // Clear all tracked timers
    for (const timer of this.timers) {
      clearInterval(timer);
    }
    this.timers.clear();

    // Clear specific timers
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.pulseDataInterval) {
      clearInterval(this.pulseDataInterval);
      this.pulseDataInterval = null;
    }

    if (this.io) {
      this.io.close();
      this.io = null;
    }

    this.connectedClients.clear();
    this.pulseRoomClients.clear();
    this.initialized = false;

    logger.info('✅ Frontend WebSocket service shutdown complete with timer cleanup');
  }
}

// Export singleton instance
export const frontendWebSocketService = new FrontendWebSocketService();
