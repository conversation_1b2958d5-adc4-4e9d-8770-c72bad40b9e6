import express from 'express';
import { storeTrade, getWalletTradeHistory } from '../controllers/tradeHistoryController';

const router = express.Router();

/**
 * @route POST /api/trade-history
 * @description Store a trade record in the database
 * @access Public
 */
router.post('/', storeTrade);

/**
 * @route GET /api/trade-history
 * @description Get trade history for a wallet
 * @access Public
 */
router.get('/', getWalletTradeHistory);

export default router;
