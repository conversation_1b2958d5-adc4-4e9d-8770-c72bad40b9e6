import express from 'express';
import { createLimitOrder, getLimitOrders, getLimitOrderById, updateLimitOrder, cancelLimitOrder, deleteLimitOrder, getLimitOrderStats } from '../controllers/limitOrderController.js';
const router = express.Router();
/**
 * @route   POST /api/limit-orders
 * @desc    Create a new limit order
 * @access  Private (requires user_id)
 */
router.post('/', createLimitOrder);
/**
 * @route   GET /api/limit-orders
 * @desc    Get limit orders for a user with filtering and pagination
 * @access  Private (requires user_id)
 */
router.get('/', getLimitOrders);
/**
 * @route   GET /api/limit-orders/stats
 * @desc    Get order statistics for a user
 * @access  Private (requires user_id)
 */
router.get('/stats', getLimitOrderStats);
/**
 * @route   GET /api/limit-orders/:id
 * @desc    Get a specific limit order by ID
 * @access  Private (requires user_id)
 */
router.get('/:id', getLimitOrderById);
/**
 * @route   PUT /api/limit-orders/:id
 * @desc    Update a limit order (mainly for cancellation)
 * @access  Private (requires user_id)
 */
router.put('/:id', updateLimitOrder);
/**
 * @route   DELETE /api/limit-orders/:id/cancel
 * @desc    Cancel a limit order
 * @access  Private (requires user_id)
 */
router.delete('/:id/cancel', cancelLimitOrder);
/**
 * @route   DELETE /api/limit-orders/:id
 * @desc    Permanently delete a limit order (only cancelled/expired orders)
 * @access  Private (requires user_id)
 */
router.delete('/:id', deleteLimitOrder);
export default router;
