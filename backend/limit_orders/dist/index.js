import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import limitOrderRoutes from './routes/limitOrderRoutes.js';
import tpslRoutes from './routes/tpslRoutes.js';
import { logger } from './utils/logger.js';
import priceMonitorService from './services/priceMonitorService.js';
// Load environment variables
dotenv.config();
// Create Express app
const app = express();
const PORT = process.env.PORT || 5002;
// Middleware
app.use(helmet()); // Security headers
app.use(cors()); // CORS support
app.use(express.json()); // JSON body parser
app.use(morgan('dev')); // Request logging
// Routes
app.use('/api/limit-orders', limitOrderRoutes);
app.use('/api/tpsl', tpslRoutes);
// Health check endpoint
app.get('/health', (req, res) => {
    const monitoringStatus = priceMonitorService.getMonitoringStatus();
    res.json({
        status: 'ok',
        service: 'limit-orders-service',
        timestamp: new Date().toISOString(),
        priceMonitoring: monitoringStatus
    });
});
// Error handling
app.use(notFoundHandler);
app.use(errorHandler);
// Price monitoring service is available but not auto-started
// Use priceMonitorService.startMonitoring() manually if needed
// Start server
app.listen(PORT, () => {
    logger.info(`🚀 Limit Orders Service running on port ${PORT}`);
    logger.info(`📝 API Documentation: http://localhost:${PORT}/api-docs`);
    logger.info(`🔍 Health Check: http://localhost:${PORT}/health`);
    logger.info('TP/SL Price Monitoring Service started');
});
// Handle graceful shutdown
process.on('SIGTERM', () => {
    logger.info('SIGTERM received. Shutting down gracefully...');
    priceMonitorService.stopMonitoring();
    process.exit(0);
});
process.on('SIGINT', () => {
    logger.info('SIGINT received. Shutting down gracefully...');
    priceMonitorService.stopMonitoring();
    process.exit(0);
});
export default app;
