import { supabase, setUserContext } from '../config/supabase.js';
import { logger } from '../utils/logger.js';
class TPSLService {
    /**
     * Create a new TP/SL order
     */
    async createTPSLOrder(orderData) {
        try {
            // Set user context for RLS
            await setUserContext(orderData.user_id);
            // Validate order data
            const validationError = this.validateTPSLOrderData(orderData);
            if (validationError) {
                return { success: false, error: validationError };
            }
            // Calculate trigger price based on percentage and direction
            const triggerPrice = this.calculateTriggerPrice(orderData.entry_price, orderData.trigger_percentage, orderData.order_type, orderData.direction);
            const orderWithTriggerPrice = {
                ...orderData,
                trigger_price: triggerPrice,
                status: 'active'
            };
            // Ensure position exists, create if it doesn't
            await this.ensurePositionExists(orderData);
            // Insert the TP/SL order
            const { data, error } = await supabase
                .from('tpsl_orders')
                .insert([orderWithTriggerPrice])
                .select()
                .single();
            if (error) {
                logger.error('Error creating TP/SL order:', error);
                return { success: false, error: error.message };
            }
            // Update position to indicate it has TP/SL
            await this.updatePositionTPSLFlags(orderData.position_id, orderData.order_type, true);
            logger.info(`TP/SL order created successfully: ${data.id}`);
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error creating TP/SL order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get positions with TP/SL orders for a user
     */
    async getPositionsWithTPSL(userId, filters) {
        try {
            await setUserContext(userId);
            let query = supabase
                .from('positions')
                .select(`
          *,
          take_profit_orders:tpsl_orders!position_id(*),
          stop_loss_orders:tpsl_orders!position_id(*)
        `)
                .eq('user_id', userId);
            if (filters?.status) {
                query = query.eq('status', filters.status);
            }
            if (filters?.token_address) {
                query = query.eq('token_address', filters.token_address);
            }
            const { data, error } = await query.order('created_at', { ascending: false });
            if (error) {
                logger.error('Error fetching positions with TP/SL:', error);
                return { success: false, error: error.message };
            }
            return { success: true, data: data || [] };
        }
        catch (error) {
            logger.error('Unexpected error fetching positions with TP/SL:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get TP/SL orders for a user
     */
    async getTPSLOrders(userId, filters) {
        try {
            await setUserContext(userId);
            let query = supabase
                .from('tpsl_orders')
                .select('*')
                .eq('user_id', userId);
            // Apply filters
            if (filters?.status)
                query = query.eq('status', filters.status);
            if (filters?.order_type)
                query = query.eq('order_type', filters.order_type);
            if (filters?.token_address)
                query = query.eq('token_address', filters.token_address);
            if (filters?.position_id)
                query = query.eq('position_id', filters.position_id);
            if (filters?.direction)
                query = query.eq('direction', filters.direction);
            // Apply pagination
            if (filters?.limit)
                query = query.limit(filters.limit);
            if (filters?.offset)
                query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1);
            // Apply ordering
            const orderBy = filters?.order_by || 'created_at';
            const orderDirection = filters?.order_direction || 'desc';
            query = query.order(orderBy, { ascending: orderDirection === 'asc' });
            const { data, error } = await query;
            if (error) {
                logger.error('Error fetching TP/SL orders:', error);
                return { success: false, error: error.message };
            }
            return { success: true, data: data || [] };
        }
        catch (error) {
            logger.error('Unexpected error fetching TP/SL orders:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Update TP/SL order
     */
    async updateTPSLOrder(orderId, userId, updateData) {
        try {
            await setUserContext(userId);
            const { data, error } = await supabase
                .from('tpsl_orders')
                .update({ ...updateData, updated_at: new Date().toISOString() })
                .eq('id', orderId)
                .eq('user_id', userId)
                .select()
                .single();
            if (error) {
                logger.error('Error updating TP/SL order:', error);
                return { success: false, error: error.message };
            }
            return { success: true, data };
        }
        catch (error) {
            logger.error('Unexpected error updating TP/SL order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Cancel TP/SL order
     */
    async cancelTPSLOrder(orderId, userId) {
        try {
            await setUserContext(userId);
            // Get the order first to update position flags
            const { data: order } = await supabase
                .from('tpsl_orders')
                .select('*')
                .eq('id', orderId)
                .eq('user_id', userId)
                .single();
            if (!order) {
                return { success: false, error: 'TP/SL order not found' };
            }
            // Cancel the order
            const { error } = await supabase
                .from('tpsl_orders')
                .update({
                status: 'cancelled',
                updated_at: new Date().toISOString()
            })
                .eq('id', orderId)
                .eq('user_id', userId);
            if (error) {
                logger.error('Error cancelling TP/SL order:', error);
                return { success: false, error: error.message };
            }
            // Check if position still has other active TP/SL orders
            await this.updatePositionTPSLFlagsAfterCancel(order.position_id, order.order_type);
            logger.info(`TP/SL order cancelled: ${orderId}`);
            return { success: true };
        }
        catch (error) {
            logger.error('Unexpected error cancelling TP/SL order:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Get TP/SL statistics for a user
     */
    async getTPSLStats(userId) {
        try {
            await setUserContext(userId);
            const today = new Date().toISOString().split('T')[0];
            // Get counts for different statuses
            const [activeTPs, activeSLs, triggeredToday, executedToday] = await Promise.all([
                supabase.from('tpsl_orders').select('id', { count: 'exact' }).eq('user_id', userId).eq('status', 'active').eq('order_type', 'take_profit'),
                supabase.from('tpsl_orders').select('id', { count: 'exact' }).eq('user_id', userId).eq('status', 'active').eq('order_type', 'stop_loss'),
                supabase.from('tpsl_orders').select('id', { count: 'exact' }).eq('user_id', userId).eq('status', 'triggered').gte('triggered_at', today),
                supabase.from('tpsl_orders').select('id', { count: 'exact' }).eq('user_id', userId).eq('status', 'executed').gte('executed_at', today)
            ]);
            const stats = {
                active_take_profits: activeTPs.count || 0,
                active_stop_losses: activeSLs.count || 0,
                triggered_today: triggeredToday.count || 0,
                executed_today: executedToday.count || 0,
                total_saved_loss: 0, // TODO: Calculate from executed stop losses
                total_profit_taken: 0 // TODO: Calculate from executed take profits
            };
            return { success: true, data: stats };
        }
        catch (error) {
            logger.error('Unexpected error fetching TP/SL stats:', error);
            return { success: false, error: 'Internal server error' };
        }
    }
    /**
     * Calculate trigger price based on entry price, percentage, and order type
     */
    calculateTriggerPrice(entryPrice, percentage, orderType, direction) {
        const multiplier = percentage / 100;
        if (direction === 'buy') {
            // For buy positions:
            // Take Profit: price goes UP (positive percentage)
            // Stop Loss: price goes DOWN (negative percentage)
            return orderType === 'take_profit'
                ? entryPrice * (1 + Math.abs(multiplier))
                : entryPrice * (1 - Math.abs(multiplier));
        }
        else {
            // For sell positions:
            // Take Profit: price goes DOWN (negative percentage)
            // Stop Loss: price goes UP (positive percentage)
            return orderType === 'take_profit'
                ? entryPrice * (1 - Math.abs(multiplier))
                : entryPrice * (1 + Math.abs(multiplier));
        }
    }
    /**
     * Validate TP/SL order data
     */
    validateTPSLOrderData(orderData) {
        if (!orderData.user_id)
            return 'User ID is required';
        if (!orderData.position_id)
            return 'Position ID is required';
        if (!orderData.token_address)
            return 'Token address is required';
        if (!['take_profit', 'stop_loss'].includes(orderData.order_type))
            return 'Invalid order type';
        if (!['buy', 'sell'].includes(orderData.direction))
            return 'Invalid direction';
        if (orderData.trigger_percentage <= 0)
            return 'Trigger percentage must be positive';
        if (orderData.amount <= 0)
            return 'Amount must be positive';
        if (orderData.amount_percentage <= 0 || orderData.amount_percentage > 100)
            return 'Amount percentage must be between 0 and 100';
        if (orderData.entry_price <= 0)
            return 'Entry price must be positive';
        if (orderData.slippage < 0 || orderData.slippage > 100)
            return 'Slippage must be between 0 and 100';
        return null;
    }
    /**
     * Ensure position exists, create if it doesn't
     */
    async ensurePositionExists(orderData) {
        try {
            // Check if position already exists
            const { data: existingPosition } = await supabase
                .from('positions')
                .select('id')
                .eq('id', orderData.position_id)
                .single();
            if (existingPosition) {
                return; // Position already exists
            }
            // Create new position
            const positionData = {
                id: orderData.position_id,
                user_id: orderData.user_id,
                token_address: orderData.token_address,
                token_name: orderData.token_name,
                token_symbol: orderData.token_symbol,
                token_image: orderData.token_image,
                pool_address: orderData.pool_address,
                dex_type: orderData.dex_type,
                direction: orderData.direction,
                entry_price: orderData.entry_price,
                current_price: orderData.current_price,
                total_amount: orderData.amount,
                remaining_amount: orderData.amount,
                sold_amount: 0,
                avg_buy_price: orderData.direction === 'buy' ? orderData.entry_price : null,
                avg_sell_price: orderData.direction === 'sell' ? orderData.entry_price : null,
                realized_pnl: 0,
                unrealized_pnl: 0,
                has_take_profit: false,
                has_stop_loss: false,
                wallet_address: orderData.wallet_address,
                wallet_id: orderData.wallet_id,
                status: 'open'
            };
            const { error } = await supabase
                .from('positions')
                .insert([positionData]);
            if (error) {
                logger.error('Error creating position:', error);
                throw new Error(`Failed to create position: ${error.message}`);
            }
            logger.info(`Position created successfully: ${orderData.position_id}`);
        }
        catch (error) {
            logger.error('Error ensuring position exists:', error);
            throw error;
        }
    }
    /**
     * Update position TP/SL flags
     */
    async updatePositionTPSLFlags(positionId, orderType, hasOrder) {
        const updateField = orderType === 'take_profit' ? 'has_take_profit' : 'has_stop_loss';
        await supabase
            .from('positions')
            .update({ [updateField]: hasOrder || false })
            .eq('id', positionId);
    }
    /**
     * Update position TP/SL flags after cancellation
     */
    async updatePositionTPSLFlagsAfterCancel(positionId, orderType) {
        // Check if there are still active orders of this type
        const { data } = await supabase
            .from('tpsl_orders')
            .select('id')
            .eq('position_id', positionId)
            .eq('order_type', orderType)
            .eq('status', 'active');
        const hasActiveOrders = data && data.length > 0;
        await this.updatePositionTPSLFlags(positionId, orderType, hasActiveOrders ?? false);
    }
}
export default new TPSLService();
