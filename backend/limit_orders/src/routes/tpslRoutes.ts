import { Router, Request, Response } from 'express';
import tpslService from '../services/tpslService.js';
import { CreateTPSLOrderData, UpdateTPSLOrderData, TPSLOrderFilters } from '../types/tpslTypes.js';
import { logger } from '../utils/logger.js';

const router = Router();

/**
 * @swagger
 * /api/tpsl/orders:
 *   post:
 *     summary: Create a new TP/SL order
 *     tags: [TP/SL Orders]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - position_id
 *               - token_address
 *               - order_type
 *               - direction
 *               - trigger_percentage
 *               - amount
 *               - amount_percentage
 *               - entry_price
 *               - current_price
 *               - wallet_address
 *               - wallet_id
 *             properties:
 *               user_id:
 *                 type: string
 *               position_id:
 *                 type: string
 *               token_address:
 *                 type: string
 *               token_name:
 *                 type: string
 *               token_symbol:
 *                 type: string
 *               token_image:
 *                 type: string
 *               pool_address:
 *                 type: string
 *               dex_type:
 *                 type: string
 *               order_type:
 *                 type: string
 *                 enum: [take_profit, stop_loss]
 *               direction:
 *                 type: string
 *                 enum: [buy, sell]
 *               trigger_percentage:
 *                 type: number
 *               amount:
 *                 type: number
 *               amount_percentage:
 *                 type: number
 *               entry_price:
 *                 type: number
 *               current_price:
 *                 type: number
 *               slippage:
 *                 type: number
 *               wallet_address:
 *                 type: string
 *               wallet_id:
 *                 type: string
 *               expires_at:
 *                 type: string
 *                 format: date-time
 *     responses:
 *       201:
 *         description: TP/SL order created successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Internal server error
 */
router.post('/orders', async (req: Request, res: Response) => {
  try {
    const orderData: CreateTPSLOrderData = req.body;
    
    logger.info('Creating TP/SL order:', { 
      user_id: orderData.user_id,
      order_type: orderData.order_type,
      token_address: orderData.token_address 
    });
    
    const result = await tpslService.createTPSLOrder(orderData);
    
    if (result.success) {
      res.status(201).json({
        success: true,
        message: 'TP/SL order created successfully',
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/tpsl/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/tpsl/positions/{userId}:
 *   get:
 *     summary: Get positions with TP/SL orders for a user
 *     tags: [TP/SL Orders]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [open, closed, partial]
 *       - in: query
 *         name: token_address
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Positions retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/positions/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { status, token_address } = req.query;
    
    const filters = {
      status: status as string,
      token_address: token_address as string
    };
    
    const result = await tpslService.getPositionsWithTPSL(userId, filters);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in GET /api/tpsl/positions:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/tpsl/orders/{userId}:
 *   get:
 *     summary: Get TP/SL orders for a user
 *     tags: [TP/SL Orders]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, triggered, executed, cancelled, expired]
 *       - in: query
 *         name: order_type
 *         schema:
 *           type: string
 *           enum: [take_profit, stop_loss]
 *       - in: query
 *         name: token_address
 *         schema:
 *           type: string
 *       - in: query
 *         name: position_id
 *         schema:
 *           type: string
 *       - in: query
 *         name: direction
 *         schema:
 *           type: string
 *           enum: [buy, sell]
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *       - in: query
 *         name: order_by
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, trigger_price]
 *           default: created_at
 *       - in: query
 *         name: order_direction
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *     responses:
 *       200:
 *         description: TP/SL orders retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/orders/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const {
      status,
      order_type,
      token_address,
      position_id,
      direction,
      limit,
      offset,
      order_by,
      order_direction
    } = req.query;
    
    const filters: TPSLOrderFilters = {
      status: status as any,
      order_type: order_type as any,
      token_address: token_address as string,
      position_id: position_id as string,
      direction: direction as any,
      limit: limit ? parseInt(limit as string) : undefined,
      offset: offset ? parseInt(offset as string) : undefined,
      order_by: order_by as any,
      order_direction: order_direction as any
    };
    
    const result = await tpslService.getTPSLOrders(userId, filters);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in GET /api/tpsl/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/tpsl/orders/{orderId}:
 *   put:
 *     summary: Update a TP/SL order
 *     tags: [TP/SL Orders]
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               user_id:
 *                 type: string
 *               trigger_percentage:
 *                 type: number
 *               amount:
 *                 type: number
 *               amount_percentage:
 *                 type: number
 *               slippage:
 *                 type: number
 *               expires_at:
 *                 type: string
 *                 format: date-time
 *               status:
 *                 type: string
 *                 enum: [active, triggered, executed, cancelled, expired]
 *     responses:
 *       200:
 *         description: TP/SL order updated successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: TP/SL order not found
 *       500:
 *         description: Internal server error
 */
router.put('/orders/:orderId', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const { user_id, ...updateData }: { user_id: string } & UpdateTPSLOrderData = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }
    
    const result = await tpslService.updateTPSLOrder(orderId, user_id, updateData);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'TP/SL order updated successfully',
        data: result.data
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in PUT /api/tpsl/orders:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/tpsl/orders/{orderId}/cancel:
 *   post:
 *     summary: Cancel a TP/SL order
 *     tags: [TP/SL Orders]
 *     parameters:
 *       - in: path
 *         name: orderId
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *             properties:
 *               user_id:
 *                 type: string
 *     responses:
 *       200:
 *         description: TP/SL order cancelled successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: TP/SL order not found
 *       500:
 *         description: Internal server error
 */
router.post('/orders/:orderId/cancel', async (req: Request, res: Response) => {
  try {
    const { orderId } = req.params;
    const { user_id } = req.body;
    
    if (!user_id) {
      return res.status(400).json({
        success: false,
        error: 'User ID is required'
      });
    }
    
    const result = await tpslService.cancelTPSLOrder(orderId, user_id);
    
    if (result.success) {
      res.json({
        success: true,
        message: 'TP/SL order cancelled successfully'
      });
    } else {
      res.status(400).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in POST /api/tpsl/orders/cancel:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * @swagger
 * /api/tpsl/stats/{userId}:
 *   get:
 *     summary: Get TP/SL statistics for a user
 *     tags: [TP/SL Orders]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: TP/SL statistics retrieved successfully
 *       500:
 *         description: Internal server error
 */
router.get('/stats/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    
    const result = await tpslService.getTPSLStats(userId);
    
    if (result.success) {
      res.json({
        success: true,
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        error: result.error
      });
    }
  } catch (error) {
    logger.error('Error in GET /api/tpsl/stats:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;