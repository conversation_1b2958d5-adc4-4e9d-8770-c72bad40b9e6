// Take Profit and Stop Loss Types
export interface TPSLOrder {
  id: string;
  user_id: string;
  position_id: string; // Links to user's position
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  order_type: 'take_profit' | 'stop_loss';
  direction: 'buy' | 'sell'; // Original position direction
  trigger_price: number; // Price that triggers the order
  trigger_percentage: number; // Percentage from entry price
  amount: number; // Amount to sell/buy when triggered
  amount_percentage: number; // Percentage of position to close
  entry_price: number; // Original position entry price
  current_price: number;
  slippage: number;
  wallet_address: string;
  wallet_id: string;
  status: 'active' | 'triggered' | 'executed' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
  triggered_at?: string;
  executed_at?: string;
  execution_tx_hash?: string;
  error_message?: string;
}

export type CreateTPSLOrderData = Omit<TPSLOrder, 'id' | 'created_at' | 'updated_at' | 'triggered_at' | 'executed_at' | 'execution_tx_hash'>;

export type UpdateTPSLOrderData = Partial<Omit<TPSLOrder, 'id' | 'user_id' | 'created_at' | 'updated_at'>>;

export interface TPSLOrderFilters {
  status?: 'active' | 'triggered' | 'executed' | 'cancelled' | 'expired';
  order_type?: 'take_profit' | 'stop_loss';
  token_address?: string;
  position_id?: string;
  direction?: 'buy' | 'sell';
  limit?: number;
  offset?: number;
  order_by?: 'created_at' | 'updated_at' | 'trigger_price';
  order_direction?: 'asc' | 'desc';
}

export interface Position {
  id: string;
  user_id: string;
  token_address: string;
  token_name: string;
  token_symbol: string;
  token_image?: string;
  pool_address: string;
  dex_type: string;
  direction: 'buy' | 'sell';
  entry_price: number;
  current_price: number;
  amount_bought: number;
  amount_sold: number;
  remaining_amount: number;
  total_invested: number;
  current_value: number;
  pnl: number;
  pnl_percentage: number;
  wallet_address: string;
  wallet_id: string;
  status: 'open' | 'closed' | 'partial';
  created_at: string;
  updated_at: string;
  // TP/SL related fields
  has_take_profit: boolean;
  has_stop_loss: boolean;
  take_profit_orders?: TPSLOrder[];
  stop_loss_orders?: TPSLOrder[];
}

export interface PositionWithTPSL extends Position {
  take_profit_orders: TPSLOrder[];
  stop_loss_orders: TPSLOrder[];
}

export interface TPSLStats {
  active_take_profits: number;
  active_stop_losses: number;
  triggered_today: number;
  executed_today: number;
  total_saved_loss: number;
  total_profit_taken: number;
}