2025-06-21 21:35:04 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-21 21:37:05 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-21 21:39:01 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 06:18:36 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:36 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:41 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:44 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:44 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:23:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:23:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:24:03 error: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 07:24:03 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836242874\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:24:03 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836242874\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:25:09 error: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 07:25:09 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836308551\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:25:09 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836308551\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:02 error: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 11:27:02 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850821119\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:02 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850821119\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:15 error: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 11:27:15 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850834523\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:15 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850834523\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:57:26 error: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "code": "23505",
  "details": "Key (id)=(a180e41b-c219-4c07-b333-d59db4bdcd4e) already exists.",
  "hint": null
}
2025-06-25 11:57:26 error: Error ensuring position exists: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:57:26 error: Unexpected error creating TP/SL order: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:57:45 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:50 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:57 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 13:21:08 error: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "code": "23505",
  "details": "Key (id)=(f8347e9f-9bf0-4fae-a98e-b69cadcfe110) already exists.",
  "hint": null
}
2025-06-25 13:21:08 error: Error ensuring position exists: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 13:21:08 error: Unexpected error creating TP/SL order: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
