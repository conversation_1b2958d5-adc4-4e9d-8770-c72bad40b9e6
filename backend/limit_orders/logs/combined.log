2025-06-21 19:45:11 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 19:45:11 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 19:45:11 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 19:45:27 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 19:46:50 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 19:46:50 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 19:46:50 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 19:50:14 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 19:50:24 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 19:50:24 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 19:50:24 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 20:07:30 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 20:07:36 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 20:07:36 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 20:07:36 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 20:13:08 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 20:18:49 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 20:18:49 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 20:18:49 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 21:24:56 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:24:56 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:29:16 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:29:16 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:29:40 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:29:40 warn: Route not found: GET /api/limit-orders/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:33:54 warn: Route not found: GET /api/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:34:03 warn: Route not found: GET /api/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:34:04 warn: Route not found: GET /api/api/limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:34:22 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 21:34:23 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 21:34:23 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 21:34:23 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 21:34:29 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 21:34:42 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 21:34:42 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 21:34:42 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-21 21:35:04 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-21 21:37:05 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-21 21:38:51 warn: Route not found: GET /limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-21 21:39:01 error: invalid input syntax for type uuid: "limit-orders" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-21 21:40:17 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-21 21:41:03 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-21 21:41:03 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-21 21:41:03 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-22 04:46:26 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-22 22:40:43 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-22 22:40:43 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-22 22:40:43 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-22 22:40:49 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-22 22:47:27 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-22 22:47:27 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-22 22:47:27 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-22 22:47:32 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-22 22:53:09 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-22 22:53:09 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-22 22:53:09 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 02:23:39 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 10:31:11 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 10:31:11 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 10:31:11 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 10:32:45 warn: Route not found: GET /limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-23 10:32:45 warn: Route not found: GET /limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-23 10:32:50 warn: Route not found: GET /limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-23 10:32:50 warn: Route not found: GET /limit-orders?user_id=did%3Aprivy%3Acm9qjpbkz014mjs0n71tbywf4&order_by=created_at&order_direction=desc&limit=50 {
  "service": "limit-orders-service"
}
2025-06-23 15:29:52 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 15:37:41 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 15:37:41 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 15:37:41 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 16:42:35 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 16:44:52 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 16:44:52 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 16:44:52 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 21:51:31 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 21:51:38 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 21:51:38 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 21:51:38 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 22:01:42 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 22:01:47 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 22:01:47 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 22:01:47 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 22:01:51 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 22:01:57 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 22:01:57 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 22:01:57 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-23 22:06:38 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-23 23:33:53 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-23 23:33:53 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-23 23:33:53 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-24 17:47:02 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 03:10:49 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 03:10:49 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 03:10:49 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 03:15:58 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:04:28 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:04:28 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:04:28 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:07:01 info: Limit order created successfully: ccc78020-e401-4309-9822-117009b8062e {
  "service": "limit-orders-service"
}
2025-06-25 05:10:41 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:10:42 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:10:42 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:10:42 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:11:05 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:13:14 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:13:14 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:13:14 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:16:10 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:19:01 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:19:01 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:19:01 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:20:15 info: Limit order created successfully: 5955218f-637c-4e92-8b21-758c62525011 {
  "service": "limit-orders-service"
}
2025-06-25 05:20:53 info: Limit order created successfully: 8a6b3247-59a1-452a-8ba0-edafb11284a7 {
  "service": "limit-orders-service"
}
2025-06-25 05:23:50 info: Limit order created successfully: 8b1db329-e6d4-4d3f-ab87-ae87e97d5224 {
  "service": "limit-orders-service"
}
2025-06-25 05:24:07 info: Limit order created successfully: 470ef0d9-770a-462d-92dd-a6c218c226c4 {
  "service": "limit-orders-service"
}
2025-06-25 05:41:53 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:41:53 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:41:53 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:41:53 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:42:44 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 05:49:43 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 05:49:43 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 05:49:43 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 05:49:43 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 06:01:29 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 06:01:29 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 06:01:29 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 06:01:29 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 06:01:29 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 06:18:36 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:36 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:41 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 06:18:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:44 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:44 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:13:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:20:31 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 07:20:56 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 07:20:56 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 07:20:56 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 07:20:56 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 07:21:26 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 07:21:44 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 07:21:44 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 07:21:44 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 07:21:44 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 07:23:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:23:42 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 07:24:02 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "0x1234567890123456789012345678901234567890"
}
2025-06-25 07:24:03 error: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 07:24:03 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836242874\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:24:03 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750836242874" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836242874\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:24:18 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 07:24:26 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 07:24:26 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 07:24:26 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 07:24:26 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 07:25:08 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "0x1234567890123456789012345678901234567890"
}
2025-06-25 07:25:09 error: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 07:25:09 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836308551\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 07:25:09 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750836308551" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750836308551\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:18:07 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 11:22:47 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 11:22:47 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 11:22:47 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 11:22:47 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 11:24:54 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 11:25:11 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 11:25:11 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 11:25:11 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 11:25:11 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 11:27:01 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "7ukRnrWVdB3m3wVVYDgQYarcf2w5wCkhexLpNPaypump"
}
2025-06-25 11:27:02 error: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 11:27:02 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850821119\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:02 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750850821119" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850821119\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:14 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "7ukRnrWVdB3m3wVVYDgQYarcf2w5wCkhexLpNPaypump"
}
2025-06-25 11:27:15 error: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "code": "22P02",
  "details": null,
  "hint": null
}
2025-06-25 11:27:15 error: Error ensuring position exists: Failed to create position: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850834523\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:27:15 error: Unexpected error creating TP/SL order: Failed to create position: invalid input syntax for type uuid: "pos_1750850834523" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: invalid input syntax for type uuid: \"pos_1750850834523\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:43:06 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 11:43:18 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 11:43:18 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 11:43:18 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 11:43:18 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 11:48:47 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 11:48:53 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 11:48:53 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 11:48:53 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 11:48:53 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 11:49:35 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 11:55:15 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 11:55:15 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 11:55:15 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 11:55:15 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 11:57:26 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 11:57:26 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "stop_loss",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 11:57:26 info: Position created successfully: a180e41b-c219-4c07-b333-d59db4bdcd4e {
  "service": "limit-orders-service"
}
2025-06-25 11:57:26 error: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "code": "23505",
  "details": "Key (id)=(a180e41b-c219-4c07-b333-d59db4bdcd4e) already exists.",
  "hint": null
}
2025-06-25 11:57:26 error: Error ensuring position exists: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:57:26 error: Unexpected error creating TP/SL order: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 11:57:27 info: TP/SL order created successfully: 9a924ee0-b251-420d-b49a-0dd6281eaf5b {
  "service": "limit-orders-service"
}
2025-06-25 11:57:45 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:50 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:57 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 11:57:59 error: Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache {
  "service": "limit-orders-service",
  "code": "PGRST200",
  "details": "Searched for a foreign key relationship between 'positions' and 'tpsl_orders' using the hint 'position_id' in the schema 'public', but no matches were found.",
  "hint": null
}
2025-06-25 12:05:54 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 12:05:54 info: Position created successfully: 68fe658e-5793-4484-94a6-f3c9f0846d8a {
  "service": "limit-orders-service"
}
2025-06-25 12:05:54 info: TP/SL order created successfully: 838537a1-e400-4aa6-a8e4-c4e522432250 {
  "service": "limit-orders-service"
}
2025-06-25 12:22:52 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 12:53:44 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "stop_loss",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 12:53:44 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 12:53:45 info: Position created successfully: c27e9995-9ca3-4c5f-9575-d7db99dba698 {
  "service": "limit-orders-service"
}
2025-06-25 12:53:45 info: TP/SL order created successfully: b083dbb3-f695-4e39-a066-f0bda21148ff {
  "service": "limit-orders-service"
}
2025-06-25 12:53:45 info: TP/SL order created successfully: cc97eb06-4ea2-4476-99d6-3294f71a252d {
  "service": "limit-orders-service"
}
2025-06-25 13:06:45 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 13:06:46 info: Position created successfully: 1d9f710b-caa3-4b4b-85ba-a6938655e24f {
  "service": "limit-orders-service"
}
2025-06-25 13:06:46 info: TP/SL order created successfully: 31e038e0-fdee-4e2c-9987-98636e6b84dc {
  "service": "limit-orders-service"
}
2025-06-25 13:20:12 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 13:20:12 info: Position created successfully: ff140954-b9fe-4a83-b5a0-c5bd07e7ece1 {
  "service": "limit-orders-service"
}
2025-06-25 13:20:12 info: TP/SL order created successfully: 10502d6e-a724-4096-ae5c-8b561db09119 {
  "service": "limit-orders-service"
}
2025-06-25 13:21:07 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "take_profit",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 13:21:08 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "stop_loss",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump"
}
2025-06-25 13:21:08 info: Position created successfully: f8347e9f-9bf0-4fae-a98e-b69cadcfe110 {
  "service": "limit-orders-service"
}
2025-06-25 13:21:08 error: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "code": "23505",
  "details": "Key (id)=(f8347e9f-9bf0-4fae-a98e-b69cadcfe110) already exists.",
  "hint": null
}
2025-06-25 13:21:08 error: Error ensuring position exists: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 13:21:08 error: Unexpected error creating TP/SL order: Failed to create position: duplicate key value violates unique constraint "positions_pkey" {
  "service": "limit-orders-service",
  "stack": "Error: Failed to create position: duplicate key value violates unique constraint \"positions_pkey\"\n    at TPSLService.ensurePositionExists (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:304:23)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async TPSLService.createTPSLOrder (file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/services/tpslService.js:24:13)\n    at async file:///www/wwwroot/redfyn-spot/backend/limit_orders/dist/routes/tpslRoutes.js:88:24"
}
2025-06-25 13:21:08 info: TP/SL order created successfully: c4fa55d5-9aca-4d56-865f-a79abac3c6be {
  "service": "limit-orders-service"
}
2025-06-25 14:14:06 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 14:16:41 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 14:16:41 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 14:16:41 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 14:16:41 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 14:19:43 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-25 15:46:08 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 15:46:08 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 15:46:08 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 15:46:08 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-25 19:04:17 info: Limit order created successfully: 1f19b7e0-5c46-4ff5-9bf5-e7932ac5fbf1 {
  "service": "limit-orders-service"
}
2025-06-25 19:07:55 info: Creating TP/SL order: {
  "service": "limit-orders-service",
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "order_type": "stop_loss",
  "token_address": "BXdFSCkf5Y9earM8a5Q2G1pyEtgFDhy8Eb66dyaVpump"
}
2025-06-25 19:07:55 info: Position created successfully: 444ef1cd-c1e1-4bab-bbf0-672cff0eceba {
  "service": "limit-orders-service"
}
2025-06-25 19:07:56 info: TP/SL order created successfully: 92efa43f-104f-4e58-a8e5-176c83fd08ca {
  "service": "limit-orders-service"
}
2025-06-25 21:56:12 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-25 21:56:12 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-25 21:56:12 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-25 21:56:12 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-26 00:57:48 info: Limit order updated successfully: 8a6b3247-59a1-452a-8ba0-edafb11284a7 {
  "service": "limit-orders-service"
}
2025-06-27 00:37:08 info: SIGINT received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-27 18:26:49 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-27 18:26:49 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-27 18:26:49 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-27 18:26:49 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
2025-06-27 20:13:44 info: SIGTERM received. Shutting down gracefully... {
  "service": "limit-orders-service"
}
2025-06-27 20:15:22 info: 🚀 Limit Orders Service running on port 5002 {
  "service": "limit-orders-service"
}
2025-06-27 20:15:22 info: 📝 API Documentation: http://localhost:5002/api-docs {
  "service": "limit-orders-service"
}
2025-06-27 20:15:22 info: 🔍 Health Check: http://localhost:5002/health {
  "service": "limit-orders-service"
}
2025-06-27 20:15:22 info: TP/SL Price Monitoring Service started {
  "service": "limit-orders-service"
}
