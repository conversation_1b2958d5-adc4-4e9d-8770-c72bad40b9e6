-- Create TP/SL orders table
CREATE TABLE IF NOT EXISTS tpsl_orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    position_id TEXT NOT NULL,
    token_address TEXT NOT NULL,
    token_name TEXT,
    token_symbol TEXT,
    token_image TEXT,
    pool_address TEXT,
    dex_type TEXT DEFAULT 'uniswap_v2',
    direction TEXT NOT NULL CHECK (direction IN ('buy', 'sell')),
    order_type TEXT NOT NULL CHECK (order_type IN ('take_profit', 'stop_loss')),
    entry_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8),
    trigger_price DECIMAL(20, 8) NOT NULL,
    trigger_percentage DECIMAL(10, 4) NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    amount_percentage DECIMAL(5, 2) DEFAULT 100.00,
    slippage DECIMAL(5, 2) DEFAULT 1.00,
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'triggered', 'cancelled', 'expired')),
    wallet_address TEXT,
    wallet_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    triggered_at TIMESTAMP WITH TIME ZONE,
    cancelled_at TIMESTAMP WITH TIME ZONE
);

-- Create positions table (if not exists)
CREATE TABLE IF NOT EXISTS positions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id TEXT NOT NULL,
    token_address TEXT NOT NULL,
    token_name TEXT,
    token_symbol TEXT,
    token_image TEXT,
    pool_address TEXT,
    dex_type TEXT DEFAULT 'uniswap_v2',
    direction TEXT NOT NULL CHECK (direction IN ('buy', 'sell')),
    entry_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8),
    total_amount DECIMAL(20, 8) NOT NULL,
    remaining_amount DECIMAL(20, 8) NOT NULL,
    sold_amount DECIMAL(20, 8) DEFAULT 0,
    avg_buy_price DECIMAL(20, 8),
    avg_sell_price DECIMAL(20, 8),
    realized_pnl DECIMAL(20, 8) DEFAULT 0,
    unrealized_pnl DECIMAL(20, 8) DEFAULT 0,
    has_take_profit BOOLEAN DEFAULT FALSE,
    has_stop_loss BOOLEAN DEFAULT FALSE,
    wallet_address TEXT,
    wallet_id TEXT,
    status TEXT DEFAULT 'open' CHECK (status IN ('open', 'closed', 'partial')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_user_id ON tpsl_orders(user_id);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_position_id ON tpsl_orders(position_id);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_status ON tpsl_orders(status);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_token_address ON tpsl_orders(token_address);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_order_type ON tpsl_orders(order_type);
CREATE INDEX IF NOT EXISTS idx_tpsl_orders_created_at ON tpsl_orders(created_at);

CREATE INDEX IF NOT EXISTS idx_positions_user_id ON positions(user_id);
CREATE INDEX IF NOT EXISTS idx_positions_token_address ON positions(token_address);
CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
CREATE INDEX IF NOT EXISTS idx_positions_created_at ON positions(created_at);

-- Enable Row Level Security (RLS)
ALTER TABLE tpsl_orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE positions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for tpsl_orders
CREATE POLICY "Users can view their own TP/SL orders" ON tpsl_orders
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own TP/SL orders" ON tpsl_orders
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own TP/SL orders" ON tpsl_orders
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own TP/SL orders" ON tpsl_orders
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create RLS policies for positions
CREATE POLICY "Users can view their own positions" ON positions
    FOR SELECT USING (auth.uid()::text = user_id);

CREATE POLICY "Users can insert their own positions" ON positions
    FOR INSERT WITH CHECK (auth.uid()::text = user_id);

CREATE POLICY "Users can update their own positions" ON positions
    FOR UPDATE USING (auth.uid()::text = user_id);

CREATE POLICY "Users can delete their own positions" ON positions
    FOR DELETE USING (auth.uid()::text = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_tpsl_orders_updated_at BEFORE UPDATE ON tpsl_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample positions for testing (optional)
-- INSERT INTO positions (
--     user_id, token_address, token_name, token_symbol, token_image,
--     pool_address, direction, entry_price, current_price,
--     total_amount, remaining_amount, wallet_address
-- ) VALUES 
-- ('sample_user_id', '0x1234...', 'Ethereum', 'ETH', 'https://example.com/eth.png',
--  '0x5678...', 'buy', 2000.00, 2100.00, 1.0, 1.0, '0xwallet...');