# Take Profit (TP) and Stop Loss (SL) Implementation

This document describes the implementation of Take Profit and Stop Loss functionality for the Redfyn Spot trading platform.

## Overview

The TP/SL system allows users to:
- Set Take Profit orders to automatically sell when price targets are reached
- Set Stop Loss orders to limit losses when prices move unfavorably
- Monitor positions with real-time price tracking
- Automatically execute trades when trigger conditions are met

## Architecture

### Backend Components

1. **Database Tables**
   - `tpsl_orders`: Stores TP/SL order data
   - `positions`: Stores user position data with TP/SL flags

2. **Services**
   - `tpslService.ts`: Core TP/SL order management
   - `priceMonitorService.ts`: Real-time price monitoring and order execution

3. **API Routes**
   - `POST /api/tpsl/orders`: Create TP/SL orders
   - `GET /api/tpsl/orders/:userId`: Get user's TP/SL orders
   - `PUT /api/tpsl/orders/:orderId`: Update TP/SL orders
   - `DELETE /api/tpsl/orders/:orderId`: Cancel TP/SL orders
   - `GET /api/tpsl/positions/:userId`: Get positions with TP/SL data

### Frontend Components

1. **Positions Table** (`Positions.tsx`)
   - Displays user positions with TP/SL status
   - Shows entry price, current price, PnL
   - Provides buttons to set TP/SL orders

2. **TP/SL Modal**
   - Form to create TP/SL orders
   - Input fields for trigger percentage, amount, slippage
   - Validation and submission handling

## Database Schema

### TP/SL Orders Table

```sql
CREATE TABLE tpsl_orders (
    id UUID PRIMARY KEY,
    user_id TEXT NOT NULL,
    position_id TEXT NOT NULL,
    token_address TEXT NOT NULL,
    order_type TEXT CHECK (order_type IN ('take_profit', 'stop_loss')),
    trigger_price DECIMAL(20, 8) NOT NULL,
    trigger_percentage DECIMAL(10, 4) NOT NULL,
    amount DECIMAL(20, 8) NOT NULL,
    status TEXT DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Positions Table

```sql
CREATE TABLE positions (
    id UUID PRIMARY KEY,
    user_id TEXT NOT NULL,
    token_address TEXT NOT NULL,
    entry_price DECIMAL(20, 8) NOT NULL,
    current_price DECIMAL(20, 8),
    remaining_amount DECIMAL(20, 8) NOT NULL,
    has_take_profit BOOLEAN DEFAULT FALSE,
    has_stop_loss BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Setup Instructions

### 1. Database Setup

```bash
# Run the SQL script to create tables
psql -d your_database -f database/create_tpsl_tables.sql
```

### 2. Backend Setup

The TP/SL service is integrated into the existing limit orders backend:

```bash
# Install dependencies (if not already installed)
cd backend/limit_orders
npm install

# Start the service
npm run dev
```

The service will automatically:
- Start the price monitoring service
- Begin checking TP/SL triggers every 10 seconds
- Execute orders when trigger conditions are met

### 3. Frontend Integration

The Positions component has been updated to include TP/SL functionality:

- Import the updated `Positions.tsx` component
- Ensure Privy authentication is configured
- The component will automatically fetch and display positions with TP/SL data

## Usage

### Setting Take Profit Orders

1. Navigate to the Positions table
2. Click the green Target icon for a position
3. Enter the trigger percentage (e.g., 20% for 20% profit)
4. Set the amount percentage to sell (default: 100%)
5. Configure slippage tolerance
6. Click "Create TP"

### Setting Stop Loss Orders

1. Navigate to the Positions table
2. Click the red Shield icon for a position
3. Enter the trigger percentage (e.g., 10% for 10% loss)
4. Set the amount percentage to sell (default: 100%)
5. Configure slippage tolerance
6. Click "Create SL"

### Monitoring Orders

- Active TP/SL orders are indicated by colored dots in the TP/SL column
- Green dot: Take Profit active
- Red dot: Stop Loss active
- The price monitoring service runs automatically in the background

## API Examples

### Create TP/SL Order

```javascript
const response = await fetch('http://localhost:5002/api/tpsl/orders', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    user_id: 'user123',
    position_id: 'pos456',
    token_address: '0x...',
    order_type: 'take_profit',
    trigger_percentage: 20.0,
    amount: 1.0,
    amount_percentage: 100.0,
    slippage: 1.0
  })
});
```

### Get User Positions

```javascript
const response = await fetch('http://localhost:5002/api/tpsl/positions/user123');
const data = await response.json();
```

## Configuration

### Price Monitoring

- **Monitoring Interval**: 10 seconds (configurable in `priceMonitorService.ts`)
- **Price Source**: Currently uses mock data - integrate with your price service
- **Execution**: Automatic when trigger conditions are met

### Customization

1. **Price Integration**: Update `fetchCurrentPrices()` in `priceMonitorService.ts` to use your actual price feeds
2. **Trade Execution**: Implement `executeTrade()` to integrate with your DEX trading logic
3. **Monitoring Frequency**: Adjust `MONITORING_INTERVAL_MS` for different update frequencies

## Security

- Row Level Security (RLS) enabled on all tables
- Users can only access their own orders and positions
- Input validation on all API endpoints
- Secure order execution with slippage protection

## Monitoring and Logging

- Health check endpoint includes monitoring status: `GET /health`
- Comprehensive logging for order creation, triggers, and execution
- Error handling with automatic retry mechanisms

## Next Steps

1. **Price Integration**: Connect to real price feeds (DEX pools, oracles)
2. **Trade Execution**: Implement actual DEX trading integration
3. **Advanced Features**: 
   - Trailing stop losses
   - Partial order execution
   - Order modification
   - Advanced order types
4. **Performance**: Optimize for high-frequency monitoring
5. **Analytics**: Add TP/SL performance tracking and statistics

## Troubleshooting

### Common Issues

1. **Orders not triggering**: Check price monitoring service status in health endpoint
2. **Database connection**: Ensure Supabase configuration is correct
3. **Authentication**: Verify Privy user authentication is working
4. **CORS issues**: Check frontend/backend CORS configuration

### Debug Commands

```bash
# Check service health
curl http://localhost:5002/health

# View logs
npm run dev # Shows real-time logs

# Check database
psql -d your_database -c "SELECT * FROM tpsl_orders WHERE status = 'active';"
```