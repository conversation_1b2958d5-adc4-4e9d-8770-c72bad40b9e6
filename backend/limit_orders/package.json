{"name": "limit_orders", "version": "1.0.0", "description": "Limit Order Service for Redfyn", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsc-watch --onSuccess \"node dist/index.js\"", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.3", "helmet": "^7.0.0", "morgan": "^1.10.0", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/cors": "^2.8.15", "@types/express": "^4.17.20", "@types/morgan": "^1.9.7", "@types/node": "^20.8.10", "@types/ws": "^8.5.8", "tsc-watch": "^6.0.4", "typescript": "^5.2.2"}}