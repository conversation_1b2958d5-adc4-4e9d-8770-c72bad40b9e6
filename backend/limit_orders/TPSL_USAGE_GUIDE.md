# TP/SL (Take Profit / Stop Loss) Usage Guide

## Overview

The TP/SL system allows users to set automated trading orders that execute when certain price conditions are met. The system has been designed with **manual execution** in mind, giving users full control over when orders are processed.

## Key Features

### 🔧 Manual Execution (No Auto-Monitoring)
- **Automatic price monitoring is DISABLED by default**
- Orders are stored in the database but require manual triggering
- This gives you complete control over execution timing
- Price monitoring can be enabled manually if needed: `priceMonitorService.startMonitoring()`

### 📊 Two Input Fields System

#### 1. **Percentage Field** (Trigger Condition)
- **Purpose**: Defines when the order should be triggered
- **Take Profit**: Positive percentage (e.g., +20% means trigger when price goes up 20%)
- **Stop Loss**: Negative percentage (e.g., -10% means trigger when price goes down 10%)
- **Example**: If entry price is $100 and you set +20%, order triggers at $120

#### 2. **Amount Field** (Order Size)
- **Purpose**: Defines how much of your position to sell/buy
- **Input**: Percentage of your total position (1-100%)
- **Default**: 100% (sell entire position)
- **Example**: If you have 10 tokens and set 50%, order will trade 5 tokens

## How to Submit TP/SL Orders

### Frontend Submission Process

1. **Open Position Table**: Navigate to your positions
2. **Click TP/SL Button**: Choose "Set TP" or "Set SL" for a position
3. **Fill Modal Fields**:
   ```
   Trigger Percentage: [+20] (for TP) or [-10] (for SL)
   Amount Percentage: [100] (what % of position to trade)
   Slippage: [1] (optional, default 1%)
   ```
4. **Submit**: Order is saved to database

### API Submission

```javascript
// Create TP/SL Order
POST /api/tpsl/orders
{
  "user_id": "user123",
  "position_id": "pos456",
  "order_type": "take_profit", // or "stop_loss"
  "trigger_percentage": 20.0,    // +20% for TP, -10% for SL
  "amount_percentage": 100.0,    // 100% of position
  "slippage": 1.0,
  "token_address": "0x...",
  "entry_price": 100.0,
  "current_price": 105.0
}
```

### Data Flow

```
User Input → Frontend Modal → API Call → Database Storage
     ↓
Manual Execution (when you decide) → Trade Execution → Position Update
```

## Database Schema

### TP/SL Orders Table
```sql
tpsl_orders (
  id UUID PRIMARY KEY,
  user_id TEXT,
  position_id TEXT,
  order_type TEXT, -- 'take_profit' or 'stop_loss'
  trigger_percentage DECIMAL(10,4), -- e.g., 20.0000 for +20%
  amount_percentage DECIMAL(5,2),   -- e.g., 100.00 for 100%
  trigger_price DECIMAL(20,8),      -- calculated: entry_price * (1 + trigger_percentage/100)
  amount DECIMAL(20,8),             -- calculated: position_amount * (amount_percentage/100)
  status TEXT DEFAULT 'active',     -- 'active', 'triggered', 'cancelled'
  created_at TIMESTAMP,
  triggered_at TIMESTAMP
)
```

## API Endpoints

### Core Operations
- `POST /api/tpsl/orders` - Create new TP/SL order
- `GET /api/tpsl/orders/:userId` - Get user's orders
- `PUT /api/tpsl/orders/:orderId` - Update order
- `DELETE /api/tpsl/orders/:orderId` - Cancel order
- `GET /api/tpsl/positions/:userId` - Get positions with TP/SL info

### Manual Execution
- `POST /api/tpsl/orders/:orderId/execute` - Manually execute order
- `GET /api/tpsl/stats/:userId` - Get TP/SL statistics

## Example Scenarios

### Scenario 1: Take Profit Order
```
Position: 10 ETH bought at $2000
TP Order: +25% trigger, 50% amount

Result:
- Trigger Price: $2500 (2000 * 1.25)
- Order Amount: 5 ETH (10 * 0.5)
- When ETH hits $2500, you can execute to sell 5 ETH
```

### Scenario 2: Stop Loss Order
```
Position: 100 USDC worth of Token X at $1.00
SL Order: -15% trigger, 100% amount

Result:
- Trigger Price: $0.85 (1.00 * 0.85)
- Order Amount: 100 tokens
- When Token X drops to $0.85, you can execute to sell all tokens
```

## Security Features

- **Row Level Security (RLS)**: Users can only access their own orders
- **Input Validation**: All percentages and amounts are validated
- **Status Tracking**: Orders have clear status progression
- **Audit Trail**: All actions are timestamped

## Manual Execution Benefits

1. **Full Control**: You decide exactly when to execute
2. **Gas Optimization**: Execute multiple orders in batches
3. **Market Timing**: Consider market conditions before execution
4. **Risk Management**: Review orders before execution
5. **No Unexpected Executions**: No surprise trades during volatile periods

## Next Steps

1. **Test the System**: Create test orders with small amounts
2. **Monitor Positions**: Use the positions table to track TP/SL status
3. **Execute When Ready**: Manually trigger orders when conditions are met
4. **Enable Auto-Monitoring** (Optional): If you want automatic execution later

## Configuration

To enable automatic monitoring (if desired):
```javascript
// In index.ts, uncomment:
priceMonitorService.startMonitoring();
```

Monitoring interval: 10 seconds (configurable in `priceMonitorService.ts`)