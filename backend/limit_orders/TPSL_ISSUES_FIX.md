# TPSL API Issues Fix

## Issues Identified

### 1. 500 Internal Server Error - Missing Foreign Key Relationship
**Error:** `Could not find a relationship between 'positions' and 'tpsl_orders' in the schema cache`

**Root Cause:** The database schema is missing a foreign key constraint between the `tpsl_orders.position_id` and `positions.id` columns. Supabase requires explicit foreign key relationships to perform joins using the `!` syntax.

**Solution:** Add the missing foreign key constraint.

### 2. 400 Bad Request - Order Creation
**Error:** `Internal server error` on POST `/api/tpsl/orders`

**Root Cause:** Likely validation errors or missing required fields in the request payload.

## Fixes

### Fix 1: Add Foreign Key Constraint

Run the following SQL in your Supabase SQL editor:

```sql
-- Add foreign key constraint between tpsl_orders and positions
ALTER TABLE tpsl_orders 
ADD CONSTRAINT fk_tpsl_orders_position_id 
FOREIGN KEY (position_id) 
REFERENCES positions(id) 
ON DELETE CASCADE 
ON UPDATE CASCADE;
```

**Important:** If you have existing orphaned records (tpsl_orders with position_id that don't exist in positions), clean them up first:

```sql
-- Clean up orphaned records (run this first if needed)
DELETE FROM tpsl_orders 
WHERE position_id NOT IN (SELECT id FROM positions);
```

### Fix 2: Verify Request Payload

Ensure your POST request to `/api/tpsl/orders` includes all required fields:

```json
{
  "user_id": "did:privy:cm9qjpbkz014mjs0n71tbywf4",
  "position_id": "a180e41b-c219-4c07-b333-d59db4bdcd4e",
  "token_address": "DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump",
  "token_name": "Moonbunny",
  "token_symbol": "bunny",
  "token_image": "https://ipfs.io/ipfs/bafkreihfsr25sst6hs6yghasgu2vqyzvj4y7ddrkefxazygxe5tabt64ey",
  "pool_address": "9BTuDhgfUfLtW9nsjZhkbYF82joJ3Wtru2kjUBzYNYH6",
  "dex_type": "pumpfun",
  "order_type": "take_profit",
  "direction": "buy",
  "trigger_percentage": 10,
  "amount": 250,
  "amount_percentage": 25,
  "entry_price": 0.00004895812148990333,
  "current_price": 0.00004895812148990333,
  "slippage": 1,
  "wallet_address": "********************************************",
  "wallet_id": "cbq2lb54zo7rtzv14i5sp75j"
}
```

### Fix 3: Alternative Query Approach (if foreign key can't be added)

If you cannot add the foreign key constraint, modify the query in `tpslService.ts` to use manual joins:

```typescript
// In getPositionsWithTPSL method, replace the current query with:
const { data: positions, error: positionsError } = await supabase
  .from('positions')
  .select('*')
  .eq('user_id', userId);

if (positionsError) {
  logger.error('Error fetching positions:', positionsError);
  return { success: false, error: positionsError.message };
}

// Manually fetch TP/SL orders for each position
const positionsWithTPSL = await Promise.all(
  positions.map(async (position) => {
    const { data: tpslOrders } = await supabase
      .from('tpsl_orders')
      .select('*')
      .eq('position_id', position.id);

    return {
      ...position,
      take_profit_orders: tpslOrders?.filter(order => order.order_type === 'take_profit') || [],
      stop_loss_orders: tpslOrders?.filter(order => order.order_type === 'stop_loss') || []
    };
  })
);
```

## Verification Steps

1. **Apply the foreign key constraint** using the SQL above
2. **Test the GET endpoint:**
   ```bash
   curl -X GET "http://localhost:5002/api/tpsl/positions/did:privy:cm9qjpbkz014mjs0n71tbywf4"
   ```

3. **Test the POST endpoint:**
   ```bash
   curl -X POST "http://localhost:5002/api/tpsl/orders" \
     -H "Content-Type: application/json" \
     -d '{"user_id":"did:privy:cm9qjpbkz014mjs0n71tbywf4","position_id":"a180e41b-c219-4c07-b333-d59db4bdcd4e","token_address":"DFFLVrbpcUMimUqsLSJzrNzhAGr7SFXf7tSkLbjApump","token_name":"Moonbunny","token_symbol":"bunny","token_image":"https://ipfs.io/ipfs/bafkreihfsr25sst6hs6yghasgu2vqyzvj4y7ddrkefxazygxe5tabt64ey","pool_address":"9BTuDhgfUfLtW9nsjZhkbYF82joJ3Wtru2kjUBzYNYH6","dex_type":"pumpfun","order_type":"take_profit","direction":"buy","trigger_percentage":10,"amount":250,"amount_percentage":25,"entry_price":0.00004895812148990333,"current_price":0.00004895812148990333,"slippage":1,"wallet_address":"********************************************","wallet_id":"cbq2lb54zo7rtzv14i5sp75j"}'
   ```

## Expected Results

After applying the fixes:
- GET `/api/tpsl/positions/:userId` should return 200 with position data
- POST `/api/tpsl/orders` should return 201 with created order data
- No more "relationship not found" errors in logs

## Additional Notes

- The foreign key constraint will ensure data integrity
- Cascade delete will automatically clean up tpsl_orders when positions are deleted
- The service already handles position creation if it doesn't exist
- Make sure your Supabase RLS policies allow the operations for the authenticated user