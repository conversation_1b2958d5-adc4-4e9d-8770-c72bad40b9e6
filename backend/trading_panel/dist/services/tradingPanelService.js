"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TradingPanelService = void 0;
const axios_1 = __importDefault(require("axios"));
class TradingPanelService {
    constructor() {
        this.MOBULA_API_KEY = process.env.MOBULA_API_KEY || 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';
    }
    async getConfig() {
        return {
            id: 'trading_panel_001',
            name: 'Trading Panel Service'
        };
    }
    async getTokenInfo(tokenAddress) {
        console.log(`Using Mobula API Key: ${this.MOBULA_API_KEY.substring(0, 5)}...`);
        const headers = {
            'authorization': this.MOBULA_API_KEY,
            'accept': 'application/json'
        };
        try {
            console.log(`Fetching token info for: ${tokenAddress}`);
            const [holdersResponse, tradesResponse] = await Promise.all([
                axios_1.default.get(`https://api.mobula.io/api/1/market/token/holders?limit=20&blockchain=solana&asset=${tokenAddress}`, { headers }),
                axios_1.default.get(`https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=solana&asset=${tokenAddress}&limit=100`, { headers })
            ]);
            const holders = holdersResponse.data.data || [];
            const trades = tradesResponse.data.data || [];
            // Sort trades by token_amount_usd and take top 10
            const topTrades = trades
                .sort((a, b) => b.token_amount_usd - a.token_amount_usd)
                .slice(0, 10);
            return { holders, trades: topTrades };
        }
        catch (error) {
            console.error('Error fetching token info from Mobula:', error);
            if (error.response) {
                console.error('Response data:', error.response.data);
                console.error('Response status:', error.response.status);
            }
            throw new Error(`Failed to fetch token info from Mobula: ${error.message}`);
        }
    }
    async getUserTrades(walletAddress, limit = 50, offset = 0, page = 1) {
        console.log(`Fetching trades for wallet: ${walletAddress}`);
        const headers = {
            'authorization': this.MOBULA_API_KEY,
            'accept': 'application/json'
        };
        try {
            const response = await axios_1.default.get(`https://api.mobula.io/api/1/wallet/trades?limit=${limit}&offset=${offset}&page=${page}&wallet=${walletAddress}&order=desc`, { headers });
            return response.data;
        }
        catch (error) {
            console.error('Error fetching user trades from Mobula:', error);
            if (error.response) {
                console.error('Response data:', error.response.data);
                console.error('Response status:', error.response.status);
            }
            throw new Error(`Failed to fetch user trades from Mobula: ${error.message}`);
        }
    }
}
exports.TradingPanelService = TradingPanelService;
//# sourceMappingURL=tradingPanelService.js.map