import { TradingPanelConfig, UserTradeResponse } from '../types';
export declare class TradingPanelService {
    private readonly MOBULA_API_KEY;
    getConfig(): Promise<TradingPanelConfig>;
    getTokenInfo(tokenAddress: string): Promise<{
        holders: any[];
        trades: any[];
    }>;
    getUserTrades(walletAddress: string, limit?: number, offset?: number, page?: number): Promise<UserTradeResponse>;
}
//# sourceMappingURL=tradingPanelService.d.ts.map