import { Request, Response } from 'express';
import { TradingPanelService } from '../services/tradingPanelService';
import { WebSocketServerService } from '../services/webSocketServer';
import { volumeAggregationService } from '../services/volumeAggregationService';

const tradingPanelService = new TradingPanelService();
let wsServer: WebSocketServerService | null = null;

// Set WebSocket server reference
export const setWebSocketServer = (server: WebSocketServerService): void => {
  wsServer = server;
};

export const getTradingPanelConfig = async (req: Request, res: Response) => {
  try {
    const config = await tradingPanelService.getConfig();
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get trading panel config'
    });
  }
};

// Get WebSocket server status
export const getWebSocketStatus = async (req: Request, res: Response) => {
  try {
    if (!wsServer) {
      return res.status(503).json({
        success: false,
        error: 'WebSocket server not initialized'
      });
    }

    const status = wsServer.getStatus();
    res.json({
      success: true,
      data: {
        ...status,
        endpoint: 'ws://localhost:5003'
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Enhanced health check with multi-pool WebSocket status and memory stats
export const healthCheck = async (req: Request, res: Response) => {
  try {
    const wsStatus = wsServer ? wsServer.getStatus() : null;
    const volumeStats = volumeAggregationService.getServiceStats();

    res.json({
      success: true,
      data: {
        service: 'trading_panel',
        status: 'OK',
        timestamp: new Date().toISOString(),
        websocket: wsStatus ? {
          totalClients: wsStatus.clientCount,
          activePools: wsStatus.poolCount,
          maxPools: wsStatus.maxPools,
          pools: wsStatus.pools.map(pool => ({
            poolAddress: pool.poolAddress,
            clients: pool.clientCount,
            connected: pool.connected,
            idleTime: Math.round(pool.idleTime / 1000) + 's'
          }))
        } : 'Not initialized',
        volumeAggregation: {
          totalPools: volumeStats.totalPools,
          totalTrades: volumeStats.totalTrades,
          memoryUsage: `${volumeStats.totalMemoryMB.toFixed(2)}MB`,
          averageTradesPerPool: Math.round(volumeStats.averageTradesPerPool),
          maxTradesPerPool: volumeStats.maxTradesPerPool
        }
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Get detailed memory statistics
export const getMemoryStats = async (req: Request, res: Response) => {
  try {
    const volumeStats = volumeAggregationService.getServiceStats();
    const memoryStats = volumeAggregationService.getMemoryStats();
    const wsStatus = wsServer ? wsServer.getStatus() : null;

    res.json({
      success: true,
      data: {
        volumeAggregation: volumeStats,
        poolMemoryBreakdown: memoryStats,
        websocketPools: wsStatus?.pools || [],
        systemMemory: {
          nodeMemory: process.memoryUsage(),
          uptime: process.uptime()
        }
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

export const getTokenInfo = async (req: Request, res: Response) => {
  try {
    const { tokenAddress } = req.params;
    if (!tokenAddress) {
      return res.status(400).json({
        success: false,
        error: 'Token address is required'
      });
    }

    const tokenInfo = await tradingPanelService.getTokenInfo(tokenAddress);
    res.json({
      success: true,
      data: tokenInfo
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

// Force cleanup of volume aggregation service
export const forceCleanup = async (req: Request, res: Response) => {
  try {
    const { poolAddress } = req.body;

    let volumeCleanup = false;
    let wsCleanup = false;

    if (poolAddress) {
      // Clean specific pool
      volumeCleanup = volumeAggregationService.forceCleanupPool(poolAddress);
      wsCleanup = wsServer ? wsServer.forceCleanupPool(poolAddress) : false;
    } else {
      // Clean all pools - not implemented for safety
      res.status(400).json({
        success: false,
        error: 'Pool address required for cleanup. Use /force-cleanup-all for complete cleanup.'
      });
      return;
    }

    res.json({
      success: true,
      data: {
        poolAddress,
        volumeAggregationCleaned: volumeCleanup,
        websocketPoolCleaned: wsCleanup,
        message: `Cleanup completed for pool: ${poolAddress}`
      }
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};

export const getUserTrades = async (req: Request, res: Response) => {
  try {
    const { walletAddress } = req.params;
    const { limit = 50, offset = 0, page = 1 } = req.query;

    if (!walletAddress) {
      return res.status(400).json({
        success: false,
        error: 'Wallet address is required'
      });
    }

    const userTrades = await tradingPanelService.getUserTrades(
      walletAddress,
      Number(limit),
      Number(offset),
      Number(page)
    );

    res.json({
      success: true,
      data: userTrades
    });
  } catch (error: any) {
    res.status(500).json({ success: false, error: error.message });
  }
};
