import { TradingPanelConfig, UserTradeResponse } from '../types';
import axios from 'axios';

export class TradingPanelService {
  private readonly MOBULA_API_KEY = process.env.MOBULA_API_KEY || 'fffa68cd-6bde-4ac5-909d-eb627d8baca0';

  async getConfig(): Promise<TradingPanelConfig> {
    return {
      id: 'trading_panel_001',
      name: 'Trading Panel Service'
    };
  }

  async getTokenInfo(tokenAddress: string): Promise<{ holders: any[]; trades: any[] }> {
    console.log(`Using Mobula API Key: ${this.MOBULA_API_KEY.substring(0, 5)}...`);

    const headers = {
      'authorization': this.MOBULA_API_KEY,
      'accept': 'application/json'
    };

    try {
      console.log(`Fetching token info for: ${tokenAddress}`);
      
      const [holdersResponse, tradesResponse] = await Promise.all([
        axios.get(`https://api.mobula.io/api/1/market/token/holders?limit=20&blockchain=solana&asset=${tokenAddress}`, { headers }),
        axios.get(`https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=solana&asset=${tokenAddress}&limit=100`, { headers })
      ]);

      const holders = holdersResponse.data.data || [];
      const trades = tradesResponse.data.data || [];

      // Sort trades by token_amount_usd and take top 10
      const topTrades = trades
        .sort((a: any, b: any) => b.token_amount_usd - a.token_amount_usd)
        .slice(0, 10);

      return { holders, trades: topTrades };
    } catch (error: any) {
      console.error('Error fetching token info from Mobula:', error);
      
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      
      throw new Error(`Failed to fetch token info from Mobula: ${error.message}`);
    }
  }

  async getUserTrades(walletAddress: string, limit: number = 50, offset: number = 0, page: number = 1): Promise<UserTradeResponse> {
    console.log(`Fetching trades for wallet: ${walletAddress}`);

    const headers = {
      'authorization': this.MOBULA_API_KEY,
      'accept': 'application/json'
    };

    try {
      const response = await axios.get(
        `https://api.mobula.io/api/1/wallet/trades?limit=${limit}&offset=${offset}&page=${page}&wallet=${walletAddress}&order=desc`,
        { headers }
      );

      return response.data;
    } catch (error: any) {
      console.error('Error fetching user trades from Mobula:', error);
      
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      
      throw new Error(`Failed to fetch user trades from Mobula: ${error.message}`);
    }
  }
}
