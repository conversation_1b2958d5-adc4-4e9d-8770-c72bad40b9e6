{"name": "trading_panel", "version": "1.0.0", "description": "Trading Panel Service for RedFyn Spot", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "nodemon --exec ts-node src/index.ts"}, "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "http": "^0.0.1-security", "ws": "^8.14.2"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/node": "^20.5.0", "@types/ws": "^8.5.5", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}}