import { useState, useEffect } from 'react';
import axios from 'axios';

// Types (matching backend)
interface UserTrade {
  chain_id: string;
  swap_type: string;
  raw_amount0: string;
  raw_amount1: string;
  amount0: number;
  amount1: number;
  ratio: number;
  price_usd_token0: number;
  price_usd_token1: number;
  date: string;
  amount_usd: number;
  pool_address: string;
  token0_address: string;
  token1_address: string;
  transaction_sender_address: string;
  transaction_hash: string;
  base: string;
  quote: string;
  side: 'buy' | 'sell';
  amount_quote: number;
  amount_base: number;
}

interface FormattedUserTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  price: string;
  trader: string;
  txHash: string;
  poolAddress: string;
  token0Address: string;
  token1Address: string;
  marketCap: number;
  tokenAmount: number;
  actualTokenAmount: number;
  [key: string]: any;
}

export const useUserTradeData = (walletAddress?: string | null) => {
  const [trades, setTrades] = useState<FormattedUserTrade[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Format a number with K, M, B, T suffix
  const formatAmountWithSuffix = (value: number): string => {
    if (value === 0) return '0';
    
    const absValue = Math.abs(value);
    
    if (absValue >= 1e12) return `${(value / 1e12).toFixed(3)}T`;
    if (absValue >= 1e9) return `${(value / 1e9).toFixed(3)}B`;
    if (absValue >= 1e6) return `${(value / 1e6).toFixed(3)}M`;
    if (absValue >= 1e3) return `${(value / 1e3).toFixed(3)}K`;
    
    // For small values, use appropriate decimal places
    if (absValue < 0.000001) return value.toExponential(2);
    if (absValue < 0.0001) return value.toFixed(8);
    if (absValue < 0.01) return value.toFixed(6);
    if (absValue < 1) return value.toFixed(4);
    
    return value.toFixed(2);
  };

  // Format user trades from API response
  const formatUserTrades = (rawTrades: UserTrade[]): FormattedUserTrade[] => {
    return rawTrades.map((trade) => {
      // Parse timestamp from date string (using UTC to avoid timezone issues)
      const timestamp = trade.date 
        ? new Date(trade.date).getTime() 
        : Date.now();
      
      // For sell trades: user is selling token1 (base) for token0 (quote/SOL)
      // For buy trades: user is buying token1 (base) with token0 (quote/SOL)
      
      // Determine if token0 is SOL (to identify which token is the base/quote)
      const isSolToken0 = trade.token0_address === 'So11111111111111111111111111111111111111112';
      
      // Get the correct amounts based on trade side and token positions
      // The base token is the non-SOL token, the quote token is SOL
      const baseAmount = isSolToken0 ? Math.abs(trade.amount1 || 0) : Math.abs(trade.amount0 || 0);
      const quoteAmount = isSolToken0 ? Math.abs(trade.amount0 || 0) : Math.abs(trade.amount1 || 0);
      
      // Format amounts with fallbacks for missing data
      // For display, we want to show the token amount (non-SOL) that the user bought/sold
      const amount = formatAmountWithSuffix(baseAmount);
      
      // USD amount of the trade
      const usdAmount = trade.amount_usd !== undefined 
        ? formatAmountWithSuffix(trade.amount_usd) 
        : '-';
      
      // Price per token (in USD)
      const price = isSolToken0
        ? (trade.price_usd_token1 !== undefined ? formatAmountWithSuffix(trade.price_usd_token1) : '-')
        : (trade.price_usd_token0 !== undefined ? formatAmountWithSuffix(trade.price_usd_token0) : '-');
      
      // Don't calculate market cap since we don't have reliable data
      const marketCap = 0;
      
      // SOL amount formatted for display
      const solAmount = formatAmountWithSuffix(quoteAmount);
      
      return {
        id: `${trade.transaction_hash || 'unknown'}-${trade.token0_address || 'unknown'}-${trade.token1_address || 'unknown'}`,
        timestamp,
        type: trade.side || 'buy',
        amount,
        usdAmount,
        price,
        solAmount, // Add formatted SOL amount for display
        trader: trade.transaction_sender_address || '-',
        txHash: trade.transaction_hash || '-',
        poolAddress: trade.pool_address || '-',
        token0Address: trade.token0_address || '-', // May be SOL address
        token1Address: trade.token1_address || '-', // May be token address
        // Don't show market cap
        marketCap: 0,
        // For compatibility with trade table:
        // tokenAmount is the SOL amount (for side panel)
        tokenAmount: quoteAmount,
        // actualTokenAmount is the token quantity (for bottom table)
        actualTokenAmount: baseAmount,
        // Include original data for reference
        originalTrade: trade,
        // Add age calculation
        age: formatTimeAgo(timestamp)
      };
    });
  };
  
  // Helper function to format time ago
  const formatTimeAgo = (timestamp: number): string => {
    const now = Date.now();
    const diffSeconds = Math.floor((now - timestamp) / 1000);
    
    if (diffSeconds < 0) {
      return 'just now'; // Handle future dates or clock skew
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
         } else {
       return `${Math.floor(diffSeconds / 86400)}d ago`;
     }
   };

  useEffect(() => {
    // Skip if no wallet address
    if (!walletAddress) {
      setTrades([]);
      setIsLoading(false);
      setError(null);
      return;
    }
    
    const fetchUserTrades = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        console.log(`Fetching user trades for Solana wallet: ${walletAddress.slice(0, 6)}...${walletAddress.slice(-4)}`);
        
        // Call the backend API using the port 4001 proxy
        const response = await axios.get(`/api/trading-panel/user-trades/${walletAddress}`);
        
        // Check if we have valid data
        if (response.data && response.data.success && response.data.data && Array.isArray(response.data.data.data)) {
          console.log(`Received ${response.data.data.data.length} user trades from API`);
          // Format the trades
          const formattedTrades = formatUserTrades(response.data.data.data);
          setTrades(formattedTrades);
        } else {
          console.warn('Invalid response format from user trades API:', response.data);
          setTrades([]);
        }
      } catch (err: any) {
        console.error('Error fetching user trades:', err);
        setError(err.message || 'Failed to fetch user trades');
        setTrades([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchUserTrades();
  }, [walletAddress]);
  
  return {
    trades,
    isLoading,
    error
  };
}; 