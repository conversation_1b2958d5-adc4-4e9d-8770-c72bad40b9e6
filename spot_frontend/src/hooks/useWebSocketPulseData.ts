import { useState, useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import { websocketService } from '@/services/websocketService';
import { processPulseData, applyDeltas } from '@/utils/pulseDeltaHandler';

console.log('🚀 [DEBUG] useWebSocketPulseData module loaded');


interface PulseDataUpdate {
  data: any;
  timestamp: number;
  source: 'websocket' | 'api';
}

interface UseWebSocketPulseDataReturn {
  pulseData: any | null;
  isConnected: boolean;
  isConnecting: boolean;
  lastUpdate: number | null;
  dataSource: 'websocket' | 'api' | null;
  error: string | null;
  reconnect: () => Promise<void>;
  forceRefresh: () => Promise<void>;
}

/**
 * Check if current route requires pulse data
 * ONLY the /pulse page should fetch pulse data
 */
const isPulseRoute = (pathname: string): boolean => {
  return pathname === '/pulse';
};

// Global state to track if WebSocket is already connected for pulse data
let globalPulseConnection: {
  isConnected: boolean;
  subscriberCount: number;
  data: any | null;
  lastUpdate: number | null;
  error: string | null;
  callbacks: Set<(data: any) => void>;
} = {
  isConnected: false,
  subscriberCount: 0,
  data: null,
  lastUpdate: null,
  error: null,
  callbacks: new Set()
};

// Global WebSocket management functions
const connectGlobalPulseWebSocket = async (): Promise<void> => {
  if (globalPulseConnection.isConnected || !websocketService) {
    return;
  }

  try {
    console.log('🔌 Connecting to pulse WebSocket (global singleton)...');
    console.log('🚀 [DEBUG] websocketService.connected:', websocketService.connected);
    console.log('🚀 [DEBUG] websocketService.inPulseRoom:', websocketService.inPulseRoom);

    if (!websocketService.connected) {
      console.log('🚀 [DEBUG] WebSocket not connected, calling connect()...');
      await websocketService.connect();
      console.log('🚀 [DEBUG] WebSocket connect() completed');
    }

    if (!websocketService.inPulseRoom) {
      console.log('🚀 [DEBUG] Not in pulse room, calling joinPulseRoom()...');
      await websocketService.joinPulseRoom();
      console.log('🚀 [DEBUG] joinPulseRoom() completed');
    }

    globalPulseConnection.isConnected = true;
    console.log('✅ Global pulse WebSocket connected');

    // Set up data listener
    const unsubscribe = websocketService.onPulseData((update: PulseDataUpdate) => {
      // Process the data to get optimized delta updates for bonding/bonded categories
      const processedData = processPulseData(update.data);
      
      // Store full processed data to global state
      if (processedData._isDelta) {
        // If we receive delta updates, apply them to existing data
        globalPulseConnection.data = applyDeltas(globalPulseConnection.data, processedData);
      } else {
        // For full data updates, just store as-is
        globalPulseConnection.data = processedData;
      }

      globalPulseConnection.lastUpdate = update.timestamp;
      globalPulseConnection.error = null;

      // Notify all subscribers with the full updated data
      globalPulseConnection.callbacks.forEach(callback => {
        callback(globalPulseConnection.data);
      });
    });

    // Store unsubscribe function for cleanup
    (globalPulseConnection as any).unsubscribe = unsubscribe;

  } catch (error) {
    console.error('❌ Failed to connect global pulse WebSocket:', error);
    globalPulseConnection.error = error instanceof Error ? error.message : 'Connection failed';
  }
};

const disconnectGlobalPulseWebSocket = (): void => {
  if (!globalPulseConnection.isConnected) {
    return;
  }

  console.log('🔌 Disconnecting global pulse WebSocket...');

  // Clean up WebSocket connection
  if (websocketService.inPulseRoom) {
    websocketService.leavePulseRoom();
  }

  if (websocketService.connected) {
    websocketService.disconnect();
  }

  // Clean up unsubscribe function
  if ((globalPulseConnection as any).unsubscribe) {
    (globalPulseConnection as any).unsubscribe();
    delete (globalPulseConnection as any).unsubscribe;
  }

  // Reset global state
  globalPulseConnection.isConnected = false;
  globalPulseConnection.data = null;
  globalPulseConnection.lastUpdate = null;
  globalPulseConnection.error = null;
  globalPulseConnection.callbacks.clear();

  console.log('✅ Global pulse WebSocket disconnected');
};

export const useWebSocketPulseData = (enableWebSocket: boolean = true): UseWebSocketPulseDataReturn => {
  const location = useLocation();
  const shouldConnectToPulse = isPulseRoute(location.pathname);

  console.log('🚀 [DEBUG] useWebSocketPulseData hook called');
  console.log('🚀 [DEBUG] - enableWebSocket:', enableWebSocket);
  console.log('🚀 [DEBUG] - location.pathname:', location.pathname);
  console.log('🚀 [DEBUG] - shouldConnectToPulse:', shouldConnectToPulse);

  const [pulseData, setPulseData] = useState<any | null>(globalPulseConnection.data);
  const [isConnected, setIsConnected] = useState(globalPulseConnection.isConnected);
  const [isConnecting, setIsConnecting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<number | null>(globalPulseConnection.lastUpdate);
  const [dataSource, setDataSource] = useState<'websocket' | 'api' | null>(null);
  const [error, setError] = useState<string | null>(globalPulseConnection.error);

  const componentId = useRef<string>(`component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  const fallbackTimer = useRef<NodeJS.Timeout | null>(null);

  // Data update callback for this component
  const handleDataUpdate = useCallback((data: any) => {
    console.log(`📊 Component ${componentId.current} received pulse data update`);
    setPulseData(data);
    setLastUpdate(Date.now());
    setDataSource('websocket');
    setError(null);
    setIsConnecting(false);
    setIsConnected(true);
  }, []);



  // Main effect to manage WebSocket connection based on route
  useEffect(() => {
    console.log('🚀 [DEBUG] useEffect triggered');
    console.log('🚀 [DEBUG] - enableWebSocket:', enableWebSocket);
    console.log('🚀 [DEBUG] - shouldConnectToPulse:', shouldConnectToPulse);
    console.log('🚀 [DEBUG] - location.pathname:', location.pathname);

    if (!enableWebSocket) {
      console.log('❌ WebSocket disabled');
      setError('WebSocket disabled');
      return;
    }

    if (!shouldConnectToPulse) {
      // Not on pulse route - clear data and ensure disconnection
      console.log(`📍 Not on pulse route (${location.pathname}) - clearing data and disconnecting`);

      // Remove this component's callback
      globalPulseConnection.callbacks.delete(handleDataUpdate);
      globalPulseConnection.subscriberCount--;

      // If no more subscribers, disconnect
      if (globalPulseConnection.subscriberCount <= 0) {
        disconnectGlobalPulseWebSocket();
      }

      // Clear local state
      setPulseData(null);
      setDataSource(null);
      setError('Not on pulse route');
      setIsConnected(false);
      setIsConnecting(false);

      return;
    }

    // On pulse route - ensure connection
    console.log(`📍 On pulse route (${location.pathname}) - ensuring WebSocket connection`);

    // Add this component as a subscriber
    globalPulseConnection.callbacks.add(handleDataUpdate);
    globalPulseConnection.subscriberCount++;

    // Set initial state from global connection
    setPulseData(globalPulseConnection.data);
    setLastUpdate(globalPulseConnection.lastUpdate);
    setError(globalPulseConnection.error);
    setIsConnected(globalPulseConnection.isConnected);

    // Connect if not already connected
    if (!globalPulseConnection.isConnected) {
      console.log('🚀 [DEBUG] Global connection not established, connecting...');
      setIsConnecting(true);
      connectGlobalPulseWebSocket().then(() => {
        console.log('🚀 [DEBUG] Global connection established successfully');
        setIsConnecting(false);
      }).catch((error) => {
        console.log('🚀 [DEBUG] Global connection failed:', error);
        setIsConnecting(false);
        setError(error.message || 'Connection failed');
      });
    } else {
      console.log('🚀 [DEBUG] Global connection already established');
    }

    // Cleanup function
    return () => {
      // Remove this component's callback
      globalPulseConnection.callbacks.delete(handleDataUpdate);
      globalPulseConnection.subscriberCount--;

      // If no more subscribers, disconnect after a delay
      if (globalPulseConnection.subscriberCount <= 0) {
        setTimeout(() => {
          if (globalPulseConnection.subscriberCount <= 0) {
            disconnectGlobalPulseWebSocket();
          }
        }, 1000); // 1 second delay to handle rapid navigation
      }
    };
  }, [enableWebSocket, shouldConnectToPulse, location.pathname, handleDataUpdate]);

  // Simple reconnect function
  const reconnect = useCallback(async () => {
    console.log('🔄 Force reconnecting to pulse WebSocket...');
    disconnectGlobalPulseWebSocket();
    if (shouldConnectToPulse) {
      await connectGlobalPulseWebSocket();
    }
  }, [shouldConnectToPulse]);

  // Simple refresh function
  const forceRefresh = useCallback(async () => {
    console.log('🔄 Force refreshing pulse data...');
    if (globalPulseConnection.isConnected) {
      // WebSocket should automatically provide fresh data
      console.log('📡 WebSocket connected - fresh data should arrive automatically');
    } else {
      console.log('❌ WebSocket not connected - cannot refresh');
    }
  }, []);

  return {
    pulseData,
    isConnected,
    isConnecting,
    lastUpdate,
    dataSource,
    error,
    reconnect,
    forceRefresh
  };
};
