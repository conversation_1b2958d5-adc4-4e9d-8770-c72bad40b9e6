import React, { useState } from "react";
import { Dialog, Switch } from "@headlessui/react";
import { FaExchangeAlt } from "react-icons/fa";

interface TpslModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { 
    order_type: 'take_profit' | 'stop_loss';
    trigger_percentage: number;
    amount_percentage: number;
    slippage: number;
  }) => void;
}

const Tpslmodal = ({ isOpen, onClose, onSubmit }: TpslModalProps) => {
  const [orderType, setOrderType] = useState<'take_profit' | 'stop_loss'>('take_profit');
  const [triggerPercentage, setTriggerPercentage] = useState<string>("");
  const [amountPercentage, setAmountPercentage] = useState<string>("100");
  const [slippage, setSlippage] = useState<string>("1");

  // Handle "Add" Button Click
  const handleAdd = () => {
    if (!triggerPercentage || !amountPercentage) {
      alert('Please fill in all required fields');
      return;
    }
    
    onSubmit({
      order_type: orderType,
      trigger_percentage: parseFloat(triggerPercentage),
      amount_percentage: parseFloat(amountPercentage),
      slippage: parseFloat(slippage)
    });
    onClose();
  };

  // Disable Add Button if required fields are empty
  const isAddDisabled = !triggerPercentage || !amountPercentage;

  return (
    <Dialog open={isOpen} onClose={onClose} className="fixed inset-0 flex items-center justify-center z-50 bg-[#181C20] bg-opacity-50">
      <div className="bg-[#181C20] border border-gray-600 p-5 rounded-lg w-[400px] shadow-lg">
        {/* Header */}
        <div className="flex justify-between text-white/50 text-2xl mb-4">
          <span>Set {orderType === 'take_profit' ? 'Take Profit' : 'Stop Loss'}</span>
          <button onClick={onClose} className="text-gray-400 hover:text-white">✕</button>
        </div>

        {/* Order Type Selection */}
        <div className="mb-4">
          <label className="block text-sm text-gray-300 mb-2">Order Type</label>
          <div className="flex gap-2">
            <button
              onClick={() => setOrderType('take_profit')}
              className={`flex-1 py-2 px-4 rounded-lg transition ${
                orderType === 'take_profit'
                  ? 'bg-green-600 text-white'
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              Take Profit
            </button>
            <button
              onClick={() => setOrderType('stop_loss')}
              className={`flex-1 py-2 px-4 rounded-lg transition ${
                orderType === 'stop_loss'
                  ? 'bg-red-600 text-white'
                  : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
              }`}
            >
              Stop Loss
            </button>
          </div>
        </div>

        {/* Trigger Percentage */}
        <div className="mb-4">
          <label className="block text-sm text-gray-300 mb-2">
            Trigger Percentage {orderType === 'take_profit' ? '(+)' : '(-)'}
          </label>
          <div className="border border-gray-600 p-2 rounded-lg flex items-center">
            <input
              type="number"
              value={triggerPercentage}
              onChange={(e) => setTriggerPercentage(e.target.value)}
              placeholder={orderType === 'take_profit' ? "20" : "10"}
              className="bg-transparent text-white text-lg outline-none flex-1"
            />
            <span className="text-gray-400 text-lg ml-2">%</span>
          </div>
          <p className="text-gray-400 text-xs mt-1">
            {orderType === 'take_profit' 
              ? 'Profit percentage to trigger sell order'
              : 'Loss percentage to trigger sell order'
            }
          </p>
        </div>

        {/* Amount Percentage */}
        <div className="mb-4">
          <label className="block text-sm text-gray-300 mb-2">
            Amount Percentage (How much to trade)
          </label>
          <div className="border border-gray-600 p-2 rounded-lg flex items-center">
            <input
              type="number"
              value={amountPercentage}
              onChange={(e) => setAmountPercentage(e.target.value)}
              placeholder="100"
              min="1"
              max="100"
              className="bg-transparent text-white text-lg outline-none flex-1"
            />
            <span className="text-gray-400 text-lg ml-2">%</span>
          </div>
          <p className="text-gray-400 text-xs mt-1">
            Percentage of your position to trade (100% = entire position)
          </p>
        </div>

        {/* Slippage */}
        <div className="mb-4">
          <label className="block text-sm text-gray-300 mb-2">
            Slippage Tolerance
          </label>
          <div className="border border-gray-600 p-2 rounded-lg flex items-center">
            <input
              type="number"
              value={slippage}
              onChange={(e) => setSlippage(e.target.value)}
              placeholder="1"
              min="0.1"
              max="50"
              step="0.1"
              className="bg-transparent text-white text-lg outline-none flex-1"
            />
            <span className="text-gray-400 text-lg ml-2">%</span>
          </div>
          <p className="text-gray-400 text-xs mt-1">
            Maximum price slippage allowed during execution
          </p>
        </div>

        {/* Submit Button */}
        <button
          className={`w-full py-3 mt-2 rounded-full transition ${
            isAddDisabled 
              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
              : orderType === 'take_profit'
                ? 'bg-green-600 text-white hover:bg-green-700'
                : 'bg-red-600 text-white hover:bg-red-700'
          }`}
          onClick={handleAdd}
          disabled={isAddDisabled}
        >
          Create {orderType === 'take_profit' ? 'Take Profit' : 'Stop Loss'} Order
        </button>
      </div>
    </Dialog>
  );
};

export default Tpslmodal;
