/**
 * pulseDeltaHandler.ts
 * Utility for tracking changes in pulse data and optimizing payloads
 */

interface TokenData {
  id?: string;
  address: string;
  bonding?: number;
  bonding_percent?: number;
  [key: string]: any;
}

// Cache of previously seen data for each category
let previousData: {
  new: Record<string, TokenData>;
  bonding: Record<string, TokenData>;
  bonded: Record<string, TokenData>;
} = {
  new: {},
  bonding: {},
  bonded: {}
};

/**
 * Converts an array of tokens to a lookup map by address
 */
const arrayToMap = (tokens: TokenData[]): Record<string, TokenData> => {
  const map: Record<string, TokenData> = {};
  tokens.forEach(token => {
    const key = token.address || token.id;
    if (key) {
      map[key] = token;
    }
  });
  return map;
};

/**
 * Processes full pulse data and returns optimized data
 * - 'new' category: Always return full data (animations need complete data)
 * - 'bonding'/'bonded' categories: Return only changed tokens
 */
export const processPulseData = (data: any): any => {
  try {
    // If no data, return as-is
    if (!data) {
      console.log('No pulse data to process, returning as-is');
      return data;
    }

    // Validate data structure
    if (!Array.isArray(data.new) && !Array.isArray(data.bonding) && !Array.isArray(data.bonded)) {
      console.warn('Pulse data missing required arrays, returning as-is', data);
      return data;
    }

    // For first data update, just store and return full data
    const isFirstUpdate = Object.keys(previousData.bonding).length === 0 || 
                          Object.keys(previousData.bonded).length === 0;
    
    if (isFirstUpdate) {
      console.log('First pulse data update, storing reference data');
      if (data.new) previousData.new = arrayToMap(data.new);
      if (data.bonding) previousData.bonding = arrayToMap(data.bonding);
      if (data.bonded) previousData.bonded = arrayToMap(data.bonded);
      return data;
    }

    // Convert new data to maps for quick lookups
    const currentBondingMap = arrayToMap(data.bonding || []);
    const currentBondedMap = arrayToMap(data.bonded || []);
    
    // Calculate changes in bonding tokens
    const bondingChanges = calculateChanges(
      previousData.bonding,
      currentBondingMap
    );
    
    // Calculate changes in bonded tokens
    const bondedChanges = calculateChanges(
      previousData.bonded,
      currentBondedMap
    );

    // Update previous data for next comparison
    previousData.new = arrayToMap(data.new || []);
    previousData.bonding = currentBondingMap;
    previousData.bonded = currentBondedMap;

    // Create optimized response
    const optimizedData = {
      ...data,
      // Always include full data for 'new' category (needed for animations)
      // But send only changed data for bonding and bonded categories
      bonding: bondingChanges,
      bonded: bondedChanges,
      // Flag to indicate this is delta data
      _isDelta: true,
      // Include original counts for debugging
      _debug: {
        originalBondingCount: (data.bonding || []).length,
        originalBondedCount: (data.bonded || []).length,
        deltaBondingCount: bondingChanges.length,
        deltaBondedCount: bondedChanges.length
      }
    };

    console.log('Pulse data optimized:', {
      original: {
        newCount: (data.new || []).length,
        bondingCount: (data.bonding || []).length, 
        bondedCount: (data.bonded || []).length
      },
      optimized: {
        newCount: (optimizedData.new || []).length,
        bondingChanges: bondingChanges.length,
        bondedChanges: bondedChanges.length
      }
    });

    return optimizedData;
  } catch (error) {
    console.error('Error processing pulse data:', error);
    // On error, return original data for safety
    return data;
  }
};

/**
 * Calculates changed tokens between previous and current data
 */
const calculateChanges = (
  previous: Record<string, TokenData>,
  current: Record<string, TokenData>
): TokenData[] => {
  const changes: TokenData[] = [];
  
  // Find added or changed tokens
  Object.values(current).forEach(token => {
    const key = token.address || token.id;
    if (!key) return;
    
    const prevToken = previous[key];
    
    // Token is new or has changed values
    if (!prevToken || 
        token.bonding !== prevToken.bonding || 
        token.bonding_percent !== prevToken.bonding_percent ||
        token.bondingPercentage !== prevToken.bondingPercentage ||
        token.price !== prevToken.price ||
        token.marketCap !== prevToken.marketCap ||
        token.volume !== prevToken.volume ||
        token.trades_24h !== prevToken.trades_24h ||
        token.holders_count !== prevToken.holders_count) {
      changes.push(token);
    }
  });
  
  // Find removed tokens (in previous but not in current)
  Object.keys(previous).forEach(key => {
    if (!current[key]) {
      // Mark as removed with special flag
      changes.push({ 
        ...previous[key], 
        _removed: true 
      });
    }
  });
  
  return changes;
};

/**
 * Applies delta updates to existing pulse data
 */
export const applyDeltas = (currentData: any, deltaData: any): any => {
  try {
    // If not delta data or no current data, use the new data as is
    if (!deltaData) {
      console.log('No delta data provided, returning current data');
      return currentData;
    }
    
    if (!deltaData._isDelta) {
      console.log('Data is not delta format, using as-is');
      return deltaData;
    }
    
    if (!currentData) {
      console.warn('No current data to apply deltas to, using delta data as-is');
      // Remove delta flag and return
      const result = { ...deltaData };
      delete result._isDelta;
      delete result._debug;
      return result;
    }

    console.log('Applying delta updates', deltaData._debug);
    
    const result = { ...currentData };
    
    // New category always gets full update
    if (deltaData.new !== undefined) {
      result.new = deltaData.new;
    }
    
    // Apply bonding changes
    if (deltaData.bonding && Array.isArray(deltaData.bonding)) {
      // Convert current bonding to map
      const bondingMap = arrayToMap(currentData.bonding || []);
      
      // Apply changes
      deltaData.bonding.forEach((token: TokenData) => {
        const key = token.address || token.id;
        if (!key) {
          console.warn('Token without address or id found during delta update', token);
          return;
        }
        
        if (token._removed) {
          // Remove the token
          console.log(`Removing bonding token: ${key}`);
          delete bondingMap[key];
        } else {
          // Add or update the token
          console.log(`Updating bonding token: ${key}`);
          bondingMap[key] = token;
        }
      });
      
      // Convert back to array
      result.bonding = Object.values(bondingMap);
    }
    
    // Apply bonded changes
    if (deltaData.bonded && Array.isArray(deltaData.bonded)) {
      // Convert current bonded to map
      const bondedMap = arrayToMap(currentData.bonded || []);
      
      // Apply changes
      deltaData.bonded.forEach((token: TokenData) => {
        const key = token.address || token.id;
        if (!key) {
          console.warn('Token without address or id found during delta update', token);
          return;
        }
        
        if (token._removed) {
          // Remove the token
          console.log(`Removing bonded token: ${key}`);
          delete bondedMap[key];
        } else {
          // Add or update the token
          console.log(`Updating bonded token: ${key}`);
          bondedMap[key] = token;
        }
      });
      
      // Convert back to array
      result.bonded = Object.values(bondedMap);
    }
    
    // Remove delta flag
    delete result._isDelta;
    delete result._debug;
    
    // Log result stats
    console.log('Delta update applied, new counts:', {
      new: (result.new || []).length,
      bonding: (result.bonding || []).length,
      bonded: (result.bonded || []).length
    });
    
    return result;
  } catch (error) {
    console.error('Error applying pulse data deltas:', error);
    // On error, return the safer of the two datasets
    if (currentData) {
      return currentData;
    } else if (deltaData) {
      const result = { ...deltaData };
      delete result._isDelta;
      delete result._debug;
      return result;
    } else {
      return {};
    }
  }
}; 