import axios from 'axios';
import { ethers } from 'ethers';
import { completeTradePancakeswap, execPancakeswap, execSwap, normalizeTokenAddress } from './swap';
import { quotePancakeswap } from './quote';

// Dynamic API base URL based on the current hostname
function getApiBaseUrl() {
  const hostname = window.location.hostname;

  // Production environments
  if (hostname.includes('crypfi.io')) {
    return 'https://redfyn.crypfi.io/api';
  }
  if (hostname.includes('lrbinfotech.com')) {
    return 'https://redfyn.lrbinfotech.com/api';
  }

  // Development environment - use Vite proxy
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return '/api'; // Use relative path to leverage Vite proxy
  }

  // Fallback
  return '/api';
}

// Define API base URL
const API_BASE_URL = getApiBaseUrl();

// Create axios instance for API calls
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: false,
  timeout: 30000, // 30 second timeout
});

// Add request interceptor for debugging
api.interceptors.request.use(
  (config) => {
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.baseURL}${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for debugging
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error(`❌ API Response Error: ${error.response?.status || 'Network Error'} ${error.config?.method?.toUpperCase()} ${error.config?.url}`, error.message);
    return Promise.reject(error);
  }
);

// No Privy token interceptors needed as we're only using client-side Privy

// Add this utility function before any API functions
/**
 * Replace null values in an object with empty strings
 * @param obj - The object to process
 * @returns The object with null values replaced by empty strings
 */
function replaceNullWithEmptyString<T>(obj: T): T {
  if (obj === null) return '' as unknown as T;
  if (typeof obj !== 'object') return obj;
  
  // Create a new object to avoid mutating the original
  const result = Array.isArray(obj) ? [] as unknown as T : {} as T;
  
  // Process each property
  Object.keys(obj as object).forEach(key => {
    const value = (obj as any)[key];
    
    // Special handling for balances object
    if (key === 'balances' && value) {
      (result as any)[key] = {
        ethereum: Array.isArray(value.ethereum) ? value.ethereum : [],
        bsc: Array.isArray(value.bsc) ? value.bsc : [],
        solana: Array.isArray(value.solana) ? value.solana : []
      };
    } else if (value === null) {
      (result as any)[key] = '';
    } else if (typeof value === 'object') {
      (result as any)[key] = replaceNullWithEmptyString(value);
    } else {
      (result as any)[key] = value;
    }
  });
  
  return result;
}

// Interface for wallet API return values
interface WalletApiResponse {
  success: boolean;
  message?: string;
  data: any;
  error?: any;
  isCanceled?: boolean;
}

// Interface for specific options
interface TokenOptions {
  limit?: number;
  network?: string;
  timeframe?: string;
}

// Helper function to check if an error is a cancellation (not a real error)
const isRequestCanceled = (error: unknown): boolean => {
  return (
    (error instanceof Error && error.name === 'AbortError') || 
    (error instanceof Error && error.name === 'CanceledError') ||
    (error instanceof Error && error.message === 'canceled')
  );
};

// Wallet-related API calls
export const walletAPI = {
  // Get wallet balances across all supported chains (ETH, BSC, Solana)
  getAllBalances: async (walletAddress: string): Promise<WalletApiResponse> => {
    try {
      if (!walletAddress) {
        console.error('No wallet address provided for getAllBalances');
        return {
          success: false,
          message: 'No wallet address provided', 
          data: null 
        };
      }
      
      // Create a controller to allow timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout (increased from 10)
      
      try {
        // Request wallet balances from API - add timestamp to avoid caching
        const timestamp = Date.now();
        const response = await api.post('/wallet/all-balances', 
          { walletAddress: walletAddress, timestamp },
          { signal: controller.signal }
        );
        
        // Clear timeout if request completes
        clearTimeout(timeoutId);
        
        return {
          success: true,
          data: replaceNullWithEmptyString(response.data)
        };
      } catch (requestError: unknown) {
        // Clear timeout
        clearTimeout(timeoutId);
        
        // Handle cancellations more gracefully (don't log as errors)
        if (isRequestCanceled(requestError)) {
          console.log("Request canceled: wallet balances fetch was aborted");
          return {
            success: false,
            message: "Request canceled",
            data: null,
            isCanceled: true // Flag to help components identify canceled requests
          };
        }
        
        // Re-throw other errors
        throw requestError;
      }
    } catch (error: unknown) {
      // Don't log cancellations as errors
      if (isRequestCanceled(error)) {
        return {
          success: false,
          message: "Request canceled",
          data: null,
          isCanceled: true
        };
      }
      
      console.error("Error fetching all wallet balances:", error);
      // Add more detailed error info for debugging
      const errorMessage = error instanceof Error ? error.message :
                          (error as any)?.response?.data?.message || 
                          "Failed to fetch wallet balances";
      
      const errorDetails = {
        status: (error as any)?.response?.status,
        statusText: (error as any)?.response?.statusText,
        data: (error as any)?.response?.data,
        url: (error as any)?.config?.url || '/wallet/all-balances',
        method: (error as any)?.config?.method || 'POST'
      };
      
      console.error("Error details:", errorDetails);
      
      return { 
        success: false,
        message: errorMessage, 
        data: null,
        error: errorDetails
      };
    }
  },
  
  // Get balances for specific tokens (used for trading panel)
  getBalancesByTokens: async (walletAddress: string, tokenAddresses: string[], network = 'ethereum'): Promise<WalletApiResponse> => {
    try {
      if (!walletAddress) {
        console.error('No wallet address provided');
        return { 
          success: false, 
          message: 'No wallet address provided', 
          data: null 
        };
      }
      
      const response = await api.post('/wallet/token-balances', {
        address: walletAddress,
        tokens: tokenAddresses,
        network 
      });
      
        return { 
        success: true, 
        data: replaceNullWithEmptyString(response.data) 
        };
    } catch (error: unknown) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 
                (error as any)?.response?.data?.message || 'Error connecting to wallet balance API', 
        data: null 
      };
    }
  },
  
  // Get token pairs from DEXScreener for a specific token
  getTokenPairs: async (tokenAddress: string, network = 'ethereum'): Promise<WalletApiResponse> => {
    try {
      if (!tokenAddress) {
        console.error('No token address provided');
        return { 
          success: false, 
          message: 'No token address provided', 
          data: null 
        };
      }
      
      const response = await api.get(`/market/token-pairs/${tokenAddress}?network=${network}`);
      
      return { 
        success: true, 
        data: replaceNullWithEmptyString(response.data) 
      };
    } catch (error: unknown) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 
                (error as any)?.response?.data?.message || 'Error connecting to token pairs API', 
        data: null 
      };
    }
  },
  
  // Identify wallet type (ethereum or solana)
  identifyWalletType: async (walletAddress: string): Promise<WalletApiResponse> => {
    try {
      if (!walletAddress) {
        console.error('No wallet address provided');
        return { 
          success: false, 
          message: 'No wallet address provided', 
          data: null 
        };
      }
      
      const response = await api.get(`/wallet/identify?address=${walletAddress}`);
      
      return { 
        success: true, 
        data: replaceNullWithEmptyString(response.data) 
      };
    } catch (error: unknown) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : 
                (error as any)?.response?.data?.message || 'Error connecting to wallet identify API', 
        data: null 
      };
    }
  },
  
  // Mock functions for client-side Privy integration
  verifyToken: () => Promise.resolve({ success: true, message: "Using client-side Privy only" }),
  getBalance: () => Promise.resolve({ balance: 0 }),
  withdraw: (data: {amount: number; address: string; token?: string}) => Promise.resolve({ 
    success: true, 
    message: "Withdrawal processed via client-side Privy" 
  }),
  
  // Scan for all tokens in a wallet using blockchain explorer APIs
  scanAllTokens: async (walletAddress: string): Promise<WalletApiResponse> => {
    try {
      if (!walletAddress) {
        console.error('No wallet address provided for scanAllTokens');
        return {
          success: false,
          message: 'No wallet address provided', 
          data: null 
        };
      }
      
      // Create a controller to allow timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout (longer for scanning)
      
      try {
        // Request to scan all tokens from the API
        const timestamp = Date.now();
        const response = await api.post('/wallet/scan-all-tokens', 
          { walletAddress: walletAddress, timestamp },
          { signal: controller.signal }
        );
        
        // Clear timeout if request completes
        clearTimeout(timeoutId);
        
        return {
          success: true,
          data: replaceNullWithEmptyString(response.data)
        };
      } catch (requestError: unknown) {
        // Clear timeout
        clearTimeout(timeoutId);
        
        // Handle abort errors specifically
        if (requestError instanceof Error && requestError.name === 'AbortError') {
          console.error("Request timed out while scanning wallet tokens");
          const result = {
            success: false,
            message: "Request timed out. Token scanning can take time, please try again later.",
            data: null
          };
          return result;
        }
        
        // Re-throw other errors
        throw requestError;
      }
    } catch (error: unknown) {
      console.error("Error scanning all wallet tokens:", error);
      // Add more detailed error info for debugging
      const errorMessage = error instanceof Error ? error.message :
                          (error as any)?.response?.data?.message || 
                          "Failed to scan wallet tokens";
      
      const errorDetails = {
        status: (error as any)?.response?.status,
        statusText: (error as any)?.response?.statusText,
        data: (error as any)?.response?.data,
        url: (error as any)?.config?.url || '/wallet/scan-all-tokens',
        method: (error as any)?.config?.method || 'POST'
      };
      
      console.error("Error details:", errorDetails);
      
      return { 
        success: false,
        message: errorMessage, 
        data: null,
        error: errorDetails
      };
    }
  },
};

// Placeholder for auth-related functionality (client-side only)
export const authAPI = {
  // These methods should be implemented using client-side Privy methods directly
  // or return placeholder data if not needed
  verifyToken: () => Promise.resolve({ success: true, message: "Using client-side Privy only" }),
  getBalance: () => Promise.resolve({ balance: 0 }),
  withdraw: (data: {amount: number; address: string; token?: string}) => Promise.resolve({ 
    success: true, 
    message: "Withdrawal processed via client-side Privy" 
  }),
};

// Home page API calls
export const homeAPI = {
  // Get token holders and top trades
  getTokenInfo: async (tokenAddress: string) => {
    try {
      const response = await api.get(`/trading-panel/token-info/${tokenAddress}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching token info for ${tokenAddress}:`, error);
      throw error;
    }
  },

  // API health check
  checkHealth: async (): Promise<any> => {
    try {
      const response = await api.get('/health');
      return replaceNullWithEmptyString(response.data);
    } catch (error: unknown) {
      return replaceNullWithEmptyString({
        status: 'error',
        message: error instanceof Error ? error.message : 
                (error as any)?.response?.data?.message || 'Unable to connect to API health endpoint',
        time: new Date().toISOString()
      });
    }
  },

  // Get wallet balances for a connected address - now using client-side only
  getWalletBalances: async (address: string) => {
    try {
      if (!address) {
        console.error('No wallet address provided');
        return { success: false, message: 'No wallet address provided', data: null };
      }
      
      const response = await api.get(`/wallet/${address}/balances`);
      return replaceNullWithEmptyString(response.data);
    } catch (error: unknown) {
      console.error('Error fetching wallet balances:', error);
      return {
        success: false, 
        message: error instanceof Error ? error.message : 
                (error as any)?.response?.data?.message || 'Error fetching wallet balances', 
        data: null 
      };
    }
  },

  // Get all home page data (highlight and radar sections)
  getHomeData: async () => {
    try {
      const response = await api.get('/home/<USER>');
      return replaceNullWithEmptyString(response.data);
    } catch (error) {
      console.error('Error fetching home data:', error);
      return replaceNullWithEmptyString({
        highlight: { gainers: [], losers: [] },
        radar: []
      });
    }
  },
  
  // Get top gainers and losers data
  getTopGainersLosers: async (limit = 5) => {
    try {
      const response = await api.get(`/home/<USER>
      return replaceNullWithEmptyString(response.data);
    } catch (error) {
      console.error('Error fetching top gainers and losers:', error);
      return replaceNullWithEmptyString({ gainers: [], losers: [] });
    }
  },
  
  // Get recently added coins
  getRecentlyAdded: async (limit = 10) => {
    try {
      const response = await api.get(`/home/<USER>
      return replaceNullWithEmptyString(response.data);
    } catch (error) {
      console.error('Error fetching recently added coins:', error);
      return [];
    }
  },
  
  // Get network-specific highlights (trending, gainers, losers, new tokens)
  getNetworkHighlights: async (network = 'universal') => {
    try {
      const response = await api.get(`/home/<USER>
      return replaceNullWithEmptyString(response.data);
    } catch (error) {
      console.error(`Error fetching network highlights for ${network}:`, error);
      return replaceNullWithEmptyString({
        trending: [],
        gainers: [],
        losers: [],
        new: []
      });
    }
  },

  getPulseData: async (userId?: string) => {
    try {
      const params = userId ? { userId } : {};
      const response = await api.get(`/home/<USER>
      return replaceNullWithEmptyString(response.data);
    } catch (error) {
      console.error(`Error fetching pulse data`, error);
      return replaceNullWithEmptyString({
        trending: [],
        gainers: [],
        losers: [],
        new: []
      });
    }
  },

fetchMarketInfo : async (type:string, params = {}) => {
    const response = await api.get(`/home/<USER>
      params: { type, ...params }
    });
    return response;
  },
  
  fetchSearchResults : async (input:string) => {
    try{
    const response = await api.get(`/home/<USER>
    return response;
    }
    catch(error){
      console.error('Error fetching search results:', error);
      return null;
    }
  },
  getMarketData: async (network:string,address:string)=>{
    try{
      const response = await api.get(`/home/<USER>
      console.log("Response",response)
      return response;
    }catch(error){
      console.error('Error fetching market data:', error);
      return null;
    }
  }
  ,
 getNetworkIcon : async (network: string): Promise<string | null> => {
    try {
      console.log("Fetching network icons...");
      
      const response = await api.get('/home/<USER>');
      console.log("Response:",response)
      const icons = response.data?.data;
      
      if (!icons || typeof icons !== 'object') {
        console.warn("No icons found in response");
        return null;
      }
      
     
  
      return icons;
    } catch (error) {
      console.error("Error fetching network icon:", error);
      return null;
    }
  }
  
  ,
  // Get connect coins (BTC, ETH, SOL)
  getConnectCoins: async () => {
    try {
      // Add cache buster to prevent over-fetching the same data
      const cacheBuster = new Date().getTime();
      
      // Add a timeout to the request to prevent hanging requests
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
      
      console.log('Fetching connect coins data...');
      try {
        const response = await api.get('/home/<USER>', {
          params: { _: cacheBuster },
          signal: controller.signal
        });
        
        // Clear the timeout
        clearTimeout(timeoutId);
        
        if (response.data && response.data.data && Array.isArray(response.data.data)) {
          console.log('Received connect coins data from API (data property)');
          return replaceNullWithEmptyString(response.data.data);
        } else if (response.data && Array.isArray(response.data)) {
          console.log('Received connect coins data from API (direct array)');
          return replaceNullWithEmptyString(response.data);
        } else {
          console.error('Invalid connect coins response format:', response.data);
          // Fallback data handled below
        }
      } catch (requestError) {
        // Clear timeout
        clearTimeout(timeoutId);
        
        // Don't treat cancellations as errors
        if (isRequestCanceled(requestError)) {
          console.log("Request canceled: connect coins fetch was aborted");
          // Still return fallback data for canceled requests
        } else {
          console.error('Error fetching connect coins:', requestError);
        }
        // Continue to fallback data
      }
      
      // Return empty array on error or invalid response
      console.log('No connect coins data available');
      return [];
    } catch (error) {
      // Don't log cancellations as errors
      if (isRequestCanceled(error)) {
        console.log("Request canceled: connect coins fetch was aborted");
      } else {
        console.error('Error fetching connect coins:', error);
      }
      
      // Return empty array on error
      return [];
    }
  },

  // Get token radar data with comprehensive information
  getTokenRadar: async (options: DexScannerOptions = {}) => {
    try {
      // Use typed options object 
      const typedOptions: Required<DexScannerOptions> = {
        limit: options.limit ?? 100,
        network: options.network ?? 'universal',
        timeframe: options.timeframe ?? '24h'
      };
      
      // Build query parameters
      const params = new URLSearchParams();
      if (typedOptions.limit) params.append('limit', String(typedOptions.limit));
      if (typedOptions.network && typedOptions.network !== 'Global') {
        // Convert 'Global' to 'universal' for the API
        const apiNetwork = typedOptions.network === 'Global' ? 'universal' : typedOptions.network.toLowerCase();
        params.append('network', apiNetwork);
      }
      if (typedOptions.timeframe) params.append('timeframe', typedOptions.timeframe);
      
      // First try to get data from our backend
      const response = await api.get(`/home/<USER>
      console.log("Response:",response.data)
      let tokenData: TokenRadarData[] = [];
      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        tokenData = response.data.data;
      } else if (Array.isArray(response.data)) {
        tokenData = response.data;
      }
      
      // Simple deduplication for Global network view - this is where duplicates occur
      if ((typedOptions.network === 'Global' || typedOptions.network === 'universal') && tokenData.length > 0) {
        const seen: Record<string, boolean> = {};
        tokenData = tokenData.filter((token: TokenRadarData) => {
          // Only deduplicate if we have a valid symbol
          if (!token.symbol) return true;
          
          const symbol = token.symbol.toUpperCase();
          if (seen[symbol]) return false;
          seen[symbol] = true;
          return true;
        });
      }
      
      // If we have tokenData, just return it after replacing nulls
      // Removed the enhancement call to enhanceTokensWithDEXScanner
      if (tokenData.length > 0) {
        // Replace null values with empty strings
        return tokenData.map((token: TokenRadarData) => replaceNullWithEmptyString(token));
      } else {
        console.error('Invalid token radar response format:', response.data);
        return [];
      }
    } catch (error) {
      // Removed the fallback logic to fetchDEXScannerData
      console.error('Error fetching token radar data from backend:', error);
      return []; // Return empty array on error
    }
  },
};

// Define interfaces for DEXScanner data
interface DexScannerPair {
  baseToken?: {
    address?: string;
    name?: string;
    symbol?: string;
  };
  quoteToken?: {
    address?: string;
    name?: string;
    symbol?: string;
  };
  pairCreatedAt?: string;
  info?: {
    websites?: Array<{url: string}>;
    socials?: Array<{type: string, url: string}>;
  };
  txns?: {
    h24?: {
      buys?: string;
      sells?: string;
    }
  };
  priceChange?: {
    h24?: string;
  };
  priceUsd?: string;
  fdv?: string;
  volume?: {
    h24?: string;
  };
  liquidity?: {
    usd?: string;
  };
  circulatingSupply?: string;
  dexId?: string;
  chainId?: string;
  url?: string;
}

interface TokenRadarData {
  id: string;
  name: string;
  symbol: string;
  image: string;
  current_price: number;
  price_change_percentage: number;
  market_cap: number | null;
  fdv: number;
  total_volume: number;
  contract: string | null;
  all_contracts: Record<string, string>;
  age_days: number | null;
  liquidity: number;
  social_links: {
    website: string | null;
    twitter: string | null;
    telegram: string | null;
  };
  network: string;
  holders: number | null;
  top_10_holders: number | null;
  top_100_holders: number | null;
  transactions_24h: number;
  traders_24h: number;
  circulating_supply: number | null;
  total_supply: number | null;
  max_supply: number | null;
}

interface DexScannerOptions {
  limit?: number;
  network?: string;
  timeframe?: string;
}


export default api;

// Re-export the imported functions for backward compatibility
export {
  execPancakeswap,
  execSwap,
  normalizeTokenAddress,
  quotePancakeswap,
  completeTradePancakeswap
};

// Get token details by ID
export const getTokenDetails = async (tokenId: string, network = 'universal'): Promise<TokenRadarData> => {
  const response = await api.get(`/market/token/${tokenId}?network=${network}`);
  return replaceNullWithEmptyString(response.data);
};

// Get market stats by symbol
export const getMarketStats = async (symbol: string, network = 'universal'): Promise<any> => {
  const response = await api.get(`/market/stats/${symbol}?network=${network}`);
  return replaceNullWithEmptyString(response.data);
};

// Export the api instance for direct use
export { api };

