import axios from 'axios';
import { SwapResponse } from '../api/solana_api';

// Define the API URL for the trade history
const API_URL = '/api/trade-history';

export interface TradeHistoryRecord {
  wallet_address: string;
  trade_type: 'Buy' | 'Sell';
  ticker: string;
  token_name: string;
  token_symbol: string;
  token_address: string;
  pool_address: string;
  amount: string;
  value: string;
  filled_price: string;
  tx_hash: string;
  dex: string;
  mev_protected?: boolean;
  slippage?: string;
  priority_level?: string;
  bribe_amount?: string;
  token_in_symbol: string;
  token_out_symbol: string;
  token_in_address: string;
  token_out_address: string;
  amount_in: string;
  amount_out: string;
}

/**
 * Save a trade to the history database
 * @param tradeData Trade history data to save
 * @returns Success status and any error message
 */
export const saveTradeHistory = async (tradeData: TradeHistoryRecord): Promise<{ success: boolean; error?: string }> => {
  try {
    console.log('Saving trade history:', tradeData);
    
    const response = await axios.post(API_URL, tradeData, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return response.data;
  } catch (error: any) {
    console.error('Error saving trade history:', error);
    
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to save trade history'
    };
  }
};

/**
 * Format and save a trade from a swap response
 * @param swapResponse The response from a swap operation
 * @param swapDetails Additional details about the swap
 * @returns Success status and any error
 */
export const saveTradeFromSwapResponse = async (
  swapResponse: SwapResponse,
  swapDetails: {
    walletAddress: string;
    direction: 'buy' | 'sell';
    amount: number;
    tokenName: string;
    tokenSymbol: string;
    tokenAddress: string;
    poolAddress: string;
    dexType: string;
    slippage: number;
    mevProtection?: boolean;
    priorityLevel?: string;
    bribeAmount?: number;
  }
): Promise<{ success: boolean; error?: string }> => {
  try {
    if (!swapResponse.success || !swapResponse.data?.signature) {
      return {
        success: false,
        error: 'Invalid swap response, missing signature or success status'
      };
    }

    const { direction, amount, walletAddress, tokenName, tokenSymbol, tokenAddress, 
      poolAddress, dexType, slippage, mevProtection, priorityLevel, bribeAmount } = swapDetails;

    const isBuy = direction === 'buy';
    const transaction = swapResponse.data;

    // Format value and price based on swap direction
    const value = isBuy ? amount.toString() : (transaction.outAmount || '0').toString();
    const filledPrice = (transaction.price || 0).toString();

    // Format token symbols based on direction
    const tokenInSymbol = isBuy ? 'SOL' : tokenSymbol;
    const tokenOutSymbol = isBuy ? tokenSymbol : 'SOL';
    const tokenInAddress = isBuy ? 'So11111111111111111111111111111111111111112' : tokenAddress; // SOL address
    const tokenOutAddress = isBuy ? tokenAddress : 'So11111111111111111111111111111111111111112'; // SOL address

    const tradeRecord: TradeHistoryRecord = {
      wallet_address: walletAddress,
      trade_type: isBuy ? 'Buy' : 'Sell',
      ticker: `${isBuy ? 'SOL' : tokenSymbol}/${isBuy ? tokenSymbol : 'SOL'}`,
      token_name: tokenName,
      token_symbol: tokenSymbol,
      token_address: tokenAddress,
      pool_address: poolAddress,
      amount: (isBuy ? transaction.outAmount : amount).toString(),
      value,
      filled_price: filledPrice,
      tx_hash: transaction.signature || transaction.transactionHash || '',
      dex: dexType,
      mev_protected: mevProtection || transaction.mevProtected || false,
      slippage: (slippage * 100).toString(),
      priority_level: priorityLevel || 'low',
      bribe_amount: bribeAmount ? bribeAmount.toString() : '0',
      token_in_symbol: tokenInSymbol,
      token_out_symbol: tokenOutSymbol,
      token_in_address: tokenInAddress,
      token_out_address: tokenOutAddress,
      amount_in: isBuy ? amount.toString() : (transaction.outAmount || '0').toString(),
      amount_out: isBuy ? (transaction.outAmount || '0').toString() : amount.toString(),
    };

    return await saveTradeHistory(tradeRecord);
  } catch (error: any) {
    console.error('Error formatting and saving trade:', error);
    
    return {
      success: false,
      error: error.message || 'Failed to format and save trade history'
    };
  }
};

/**
 * Get trade history for a wallet
 * @param walletAddress Wallet address to get history for
 * @param limit Number of records to return (default: 100)
 * @param offset Pagination offset (default: 0)
 * @returns Trade history data
 */
export const getTradeHistory = async (
  walletAddress: string,
  limit: number = 100,
  offset: number = 0
): Promise<{ success: boolean; data?: any[]; error?: string; count?: number }> => {
  try {
    const response = await axios.get(API_URL, {
      params: {
        wallet_address: walletAddress,
        limit,
        offset
      }
    });

    return response.data;
  } catch (error: any) {
    console.error('Error getting trade history:', error);
    
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to get trade history'
    };
  }
};

export default {
  saveTradeHistory,
  saveTradeFromSwapResponse,
  getTradeHistory
};
