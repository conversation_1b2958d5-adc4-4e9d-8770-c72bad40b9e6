/* Pulse Component Scroll Optimizations */
.pulse-scrollable {
  /* Hardware acceleration */
  transform: translateZ(0);
  will-change: transform;
  backface-visibility: hidden;
  
  /* Scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(100, 116, 139, 0.5) rgba(30, 41, 59, 0.3);
}

.pulse-scrollable::-webkit-scrollbar {
  width: 6px;
}

.pulse-scrollable::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.3);
  border-radius: 10px;
}

.pulse-scrollable::-webkit-scrollbar-thumb {
  background: rgba(100, 116, 139, 0.5);
  border-radius: 10px;
  border: 1px solid rgba(30, 41, 59, 0.3);
}

.pulse-scrollable::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 116, 139, 0.7);
}

/* Hardware accelerated card rendering */
.pulse-card {
  transform: translateZ(0);
  will-change: transform;
  contain: content;
  position: relative;
  margin: 8px 0;
  transition: transform 0.2s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 1;
}

.pulse-card:hover {
  transform: scale(1.01);
  z-index: 5;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Optimized animations */
@keyframes slide-in-elegant {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-elegant {
  animation: slide-in-elegant 0.4s ease-out forwards;
}

/* Container optimization */
.pulse-container {
  contain: content;
}

/* Prevent automatic scrolling on hover */
.pulse-scrollable .pulse-card:hover {
  contain: layout style paint;
}

/* Settings dropdown z-index and overflow fixes */
.pulse-container {
  position: relative;
  z-index: 1;
}

/* Ensure dropdowns appear above pulse content */
.settings-dropdown {
  position: fixed !important;
  z-index: 10000 !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Prevent overflow issues with dropdowns */
.pulse-scrollable {
  /* Removed overflow: visible !important to allow normal scrolling */
}

/* Buy button hover state for scroll control */
.buy-button-hover-active {
  z-index: 100;
}

