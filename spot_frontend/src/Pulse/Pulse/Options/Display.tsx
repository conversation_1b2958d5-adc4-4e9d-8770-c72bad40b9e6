
import { useEffect, useRef } from 'react';
import {
  Setting<PERSON>,
  Circle,
  RotateCw,
  Grid,
  Zap,
  Sun,
} from 'lucide-react';
interface CustomRows {
  marketCap: boolean;
  volume: boolean;
  tx: boolean;
  socials: boolean;
  holders: boolean;
  proTraders: boolean;
  devMigrations: boolean;
}
const customRowKeys: (keyof CustomRows)[] = [
  'marketCap',
  'volume',
  'tx',
  'socials',
  'holders',
  'proTraders',
  'devMigrations',
];
type SettingsType = {
  greyButtons: boolean;
  circleImages: boolean;
  progressBar: boolean;
  compactTables: boolean;
  // add any other keys that you have here too
};

interface DisplayProps {
  metricsSize: string;
  showDropdown: boolean;
  setShowDropdown: React.Dispatch<React.SetStateAction<boolean>>;
  setMetricsSize: React.Dispatch<React.SetStateAction<string>>;
  quickBuySize: string;
  setQuickBuySize: React.Dispatch<React.SetStateAction<string>>;
  settings: Record<string, boolean>;
  setSettings: React.Dispatch<React.SetStateAction<SettingsType>>;
  customRows: Record<string, boolean>;
  setCustomRows: React.Dispatch<React.SetStateAction<CustomRows>>;
}


interface ToggleItemProps {
  label: string;
  icon: React.ReactNode;
  value: boolean;
  onToggle: () => void;
}

function ToggleItem({ label, icon, value, onToggle }: ToggleItemProps) {

  
  return (
    <button
      className="w-full flex items-center justify-between py-2"
      onClick={onToggle}
    >
      <div className="flex items-center">{icon}<span>{label}</span></div>
      <div className={`w-4 h-4 rounded-full ${value ? 'bg-white' : 'border border-white'}`} />
    </button>
  );
}

export default function Display({
  metricsSize,
  showDropdown,
  setShowDropdown,
  setMetricsSize,
  quickBuySize,
  setQuickBuySize,
  settings,
  setSettings,
  customRows,
  setCustomRows,
}: DisplayProps) {
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  const toggleSetting = (setting: keyof SettingsType) => {
    setSettings(prev => ({ ...prev, [setting]: !prev[setting] }));
  };
  

  const toggleCustomRow = (row: keyof CustomRows) => {
    setCustomRows(prev => ({ ...prev, [row]: !prev[row] }));
  };
  

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current?.contains(event.target as Node)
      ) {
        setShowDropdown(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [setShowDropdown]);
  useEffect(() => {
    localStorage.setItem('display_settings', JSON.stringify(settings));
  }, [settings]);
  
  useEffect(() => {
    localStorage.setItem('custom_rows', JSON.stringify(customRows));
  }, [customRows]);
  
  useEffect(() => {
    localStorage.setItem('metrics_size', metricsSize);
  }, [metricsSize]);
  
  useEffect(() => {
    localStorage.setItem('quick_buy_size', quickBuySize);
  }, [quickBuySize]);
  return (
    <div className="relative" style={{ zIndex: 9999 }}>
      <div className="flex justify-center">
        <button
          ref={buttonRef}
          className="flex items-center gap-2 px-4 py-2 rounded-full bg-zinc-800 hover:bg-zinc-700 transition-colors"
          onClick={() => setShowDropdown(prev => !prev)}
        >
          <Settings className="w-5 h-5" />
          <span className='text-base'>Display</span>
        </button>
      </div>

      {showDropdown && (
        <div
          ref={dropdownRef}
          className="absolute mt-2 right-0 w-96 bg-zinc-900 rounded-lg shadow-lg p-4 text-white"
          style={{
            zIndex: 10000,
            boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1)'
          }}
        >
          <h2 className="text-lg mb-4 font-semibold">Metrics</h2>
          <div className="grid grid-cols-2 gap-3 mb-6">
            {['small', 'large'].map(size => (
              <button
                key={size}
                onClick={() => setMetricsSize(size)}
                className={`py-3 px-4 rounded-lg flex flex-col items-center  ${
                  metricsSize === size
                    ? 'bg-zinc-700 text-white border border-gray-500'
                    : ' bg-zinc-800'
                }`}
              >
                <span className="text-lg font-medium">MC 77K</span>
                <span className="text-sm capitalize">{size}</span>
              </button>
            ))}
          </div>

          <h2 className="text-lg mb-4 font-semibold">Quick Buy</h2>
          <div className="grid grid-cols-3 gap-3 mb-6">
            {['small', 'large', 'mega'].map(size => (
              <button
                key={size}
                onClick={() => setQuickBuySize(size)}
                className={`py-3 px-4 rounded-lg flex flex-col items-center  ${
                  quickBuySize === size
                    ? 'bg-zinc-700 text-white border border-gray-500'
                    : ' bg-zinc-800'
                }`}
              >
                <div className="bg-blue-600/90 rounded-full px-2 py-1 flex items-center mb-1">
                  <Zap fill="black" className="w-3 h-3 mr-1" />
                  <span className="text-black">7</span>
                </div>
                <span className="text-sm capitalize">{size}</span>
              </button>
            ))}
          </div>

          <h2 className="text-lg mb-4 font-semibold">Appearance</h2>
          <div className="space-y-4 mb-6">
            <ToggleItem
              label="Grey Buttons"
              icon={<Sun className="w-5 h-5 mr-3" />}
              value={settings.greyButtons}
              onToggle={() => toggleSetting('greyButtons')}
            />
            <ToggleItem
              label="Circle Images"
              icon={<Circle className="w-5 h-5 mr-3" />}
              value={settings.circleImages}
              onToggle={() => toggleSetting('circleImages')}
            />
            <ToggleItem
              label="Progress Bar"
              icon={<RotateCw className="w-5 h-5 mr-3" />}
              value={settings.progressBar}
              onToggle={() => toggleSetting('progressBar')}
            />
            <ToggleItem
              label="Compact Tables"
              icon={<Grid className="w-5 h-5 mr-3" />}
              value={settings.compactTables}
              onToggle={() => toggleSetting('compactTables')}
            />
          </div>

          <h2 className="text-lg mb-4 font-semibold">Customize Rows</h2>
          <div className="grid grid-cols-3 gap-2">
    {customRowKeys.map(row => (
      <button
        key={row}
        className={`py-2 px-3 rounded-lg text-center text-sm capitalize ${
          customRows[row]
            ? 'bg-zinc-700 text-white'
            : 'bg-zinc-950 border border-zinc-800'
        }`}
        onClick={() => toggleCustomRow(row)}
      >
        {row.replace(/([A-Z])/g, ' $1')}
      </button>
    ))}
  </div>
        </div>
      )}
    </div>
  );
}
