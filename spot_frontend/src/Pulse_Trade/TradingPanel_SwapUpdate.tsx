// This code snippet updates the executeMarketOrder function in TradingPanel.tsx
// to include token name and symbol in swap request and store trade history

// Update SwapRequest to include token details
const swapRequest: SwapRequest = {
  tokenAddress: activePulseToken.address,
  poolAddress: activePulseToken.pool_address || '',
  dexType: activePulseToken.exchange_name.toLowerCase(),
  amount: parseFloat(amount),
  direction: isBuy ? 'buy' : 'sell',
  slippage: currentPresetSettings.slippage / 100, // Convert percentage to decimal
  walletAddress: walletInfo.address,
  walletId: walletInfo.id,
  
  // Add token name and symbol
  tokenName: activePulseToken.name || '',
  tokenSymbol: activePulseToken.symbol || '',

  // MEV Protection settings from preset - only enable for known MEV modes
  mevProtection: currentPresetSettings.mevMode !== 'Off',
  bribeAmount: currentPresetSettings.bribe, // Send as SOL, backend will convert properly
  priorityLevel: currentPresetSettings.mevMode === 'Sec.' ? 'veryHigh' :   // Secure mode: maximum priority
              currentPresetSettings.mevMode === 'Red.' ? 'high' :        // Reduced mode: high priority
              currentPresetSettings.mevMode === 'Off' ? 'low' :       // MEV off: reliable medium priority
              'low', // Fallback for any unknown modes
  priorityFee: currentPresetSettings.priority // Send as SOL, backend will convert properly
};

// After successful swap, store trade history
if (result.success && result.data) {
  setExecutionStage('Transaction confirmed!');
  setExecutionProgress(100);

  // Extract transaction signature from response
  const transactionSignature = result.data.signature || result.data.transactionHash || '';
  const tokenSymbol = activePulseToken.symbol || 'TOKEN';

  // Store trade history in database
  try {
    const tradeHistoryService = await import('../../services/tradeHistoryService');
    tradeHistoryService.saveTradeFromSwapResponse(result, {
      walletAddress: walletInfo.address,
      direction: isBuy ? 'buy' : 'sell',
      amount: parseFloat(amount),
      tokenName: activePulseToken.name || '',
      tokenSymbol: activePulseToken.symbol || '',
      tokenAddress: activePulseToken.address,
      poolAddress: activePulseToken.pool_address || '',
      dexType: activePulseToken.exchange_name.toLowerCase(),
      slippage: currentPresetSettings.slippage / 100,
      mevProtection: currentPresetSettings.mevMode !== 'Off',
      priorityLevel: swapRequest.priorityLevel,
      bribeAmount: currentPresetSettings.bribe
    }).then(saveResult => {
      if (saveResult.success) {
        console.log('Trade history saved successfully');
      } else {
        console.error('Failed to save trade history:', saveResult.error);
      }
    });
  } catch (tradeHistoryError) {
    console.error('Error saving trade history:', tradeHistoryError);
  }

  // Show success toast with Solscan link
  if (transactionSignature) {
    showSwapSuccessToast(
      isBuy ? 'buy' : 'sell',
      tokenSymbol,
      transactionSignature,
      result.data.solscanUrl // Use provided URL if available
    );
  } else {
    showSwapInfoToast(`Successfully ${isBuy ? 'bought' : 'sold'} ${tokenSymbol}`);
  }
