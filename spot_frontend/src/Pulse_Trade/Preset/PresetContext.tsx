import React, { createContext, useContext, useState, ReactNode } from 'react';

type PresetTab = 'buy' | 'sell';

type PresetSettings = {
  slippage: number;
  priority: number;
  bribe: number;
  maxFee: string;
  mevMode: string;
  rpc: string;
  autoFee: boolean;
};

type PresetData = {
  [presetId: number]: {
    buy: PresetSettings;
    sell: PresetSettings;
  };
};

interface PresetContextType {
  activePreset: number | null;
  setActivePreset: (id: number | null) => void;
  activeTab: PresetTab;
  setActiveTab: (tab: PresetTab) => void;
  presetData: PresetData;
  setPresetData: React.Dispatch<React.SetStateAction<PresetData>>;
  handleInputChange: (key: string, value: any) => void;
}

const PresetContext = createContext<PresetContextType | undefined>(undefined);

const defaultPresetData: PresetData = {
  1: {
    buy: {
      slippage: 15, priority: 0.0003, bribe: 0.0001,
      maxFee: '0.1', mevMode: 'Off', rpc: 'https://rpc1...', autoFee: true,
    },
    sell: {
      slippage: 10, priority: 0.0002, bribe: 0.00005,
      maxFee: '0.08', mevMode: 'Off', rpc: 'https://sellrpc1...', autoFee: true,
    }
  },
  2: {
    buy: {
      slippage: 21, priority: 0, bribe: 0,
      maxFee: '0.0', mevMode: 'Red.', rpc: 'https://a...e.com', autoFee: true,
    },
    sell: {
      slippage: 19, priority: 0.0001, bribe: 0.00005,
      maxFee: '0.03', mevMode: 'Red.', rpc: 'https://sellrpc2...', autoFee: true,
    }
  },
  3: {
    buy: {
      slippage: 25, priority: 0.001, bribe: 0.0002,
      maxFee: '0.2', mevMode: 'Sec.', rpc: 'https://rpc3...', autoFee: false,
    },
    sell: {
      slippage: 18, priority: 0.00015, bribe: 0.0001,
      maxFee: '0.12', mevMode: 'Sec.', rpc: 'https://sellrpc3...', autoFee: false,
    }
  },
};

export const PresetProvider = ({ children }: { children: ReactNode }) => {
  const [activePreset, setActivePreset] = useState<number | null>(1);
  const [activeTab, setActiveTab] = useState<PresetTab>('buy');
  const [presetData, setPresetData] = useState<PresetData>(defaultPresetData);

  const handleInputChange = (key: string, value: any) => {
    if (activePreset === null) return;
    setPresetData(prev => ({
      ...prev,
      [activePreset]: {
        ...prev[activePreset],
        [activeTab]: {
          ...prev[activePreset][activeTab],
          [key]: value,
        },
      },
    }));
  };

  return (
    <PresetContext.Provider
      value={{
        activePreset,
        setActivePreset,
        activeTab,
        setActiveTab,
        presetData,
        setPresetData,
        handleInputChange,
      }}
    >
      {children}
    </PresetContext.Provider>
  );
};

export const usePreset = () => {
  const context = useContext(PresetContext);
  if (!context) throw new Error("usePreset must be used within a PresetProvider");
  return context;
};
