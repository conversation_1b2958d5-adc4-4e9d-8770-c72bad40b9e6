import { useState, useMemo, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { TrendingUp, User, RefreshCw } from 'lucide-react';
import { PresetProvider, usePreset } from '../Preset/PresetContext';

import Orders from './Orders';
import Holders from './Holders';
import Traders from './Traders';
import Top_Traders from './Top_Traders';
import Positions from './Positions';
import DevTokens from './DevTokens';
import { Fuel } from 'lucide-react';
import { Filter } from 'lucide-react';
// Mock components for demonstration
import { ShieldCheckIcon, Shield, ShieldOff } from 'lucide-react';


// Trading Interface Modal Component
const TradingInterface = ({ onClose }: { onClose: () => void }) => {
  const [activeTab, setActiveTab] = useState(1);
  const [sellMode, setSellMode] = useState('percent');

  const SOL = "https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png";
  
  const toggleSellMode = () => {
    setSellMode(sellMode === 'percent' ? 'sol' : 'percent');
  };
  const { presetData } = usePreset();

  
  const buySettings = presetData[activeTab]?.buy || {};
  const sellSettings = presetData[activeTab]?.sell || {};

  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[999999]">
      <div className="w-full max-w-md bg-[#181C20] text-white rounded-lg border border-gray-600 shadow-xl flex flex-col">
        {/* Navigation */}
        <div className="flex justify-between items-center px-3 py-2 border-b border-gray-800">
          <div className="flex space-x-2">
            {[1, 2, 3].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-2 py-1 rounded ${
                  activeTab === tab
                    ? "text-white font-medium border-b-2 border-emerald-400"
                    : "text-gray-400 hover:text-white"
                }`}
              >
                {tab}
              </button>
            ))}
          </div>
          <button className="text-gray-400" onClick={onClose}>
            ✕
          </button>
        </div>

        {/* BUY Section */}
        <div className="p-3 border-b border-gray-800">
          <div className="flex justify-between items-center mb-3">
            <span className="text-lg font-medium">Buy</span>
            <div className="flex items-center px-1 py-2 border-r border-gray-800">
              <img src={SOL} alt="SOL" className="w-5 h-5 mr-1" />
              <span>0</span>
            </div>
          </div>

          <div className="grid grid-cols-4 gap-2 mb-3">
            {["0.01", "0.1", "1", "10"].map((val) => (
              <button
                key={val}
                className="border border-emerald-500 rounded-full py-2 text-emerald-500 text-center bg-transparent"
              >
                {val}
              </button>
            ))}
          </div>

          <TradeStats {...buySettings} />
        </div>

        {/* SELL Section */}
        <div className="p-3 border-b border-gray-800">
          <div className="flex justify-between items-center mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-lg font-medium">Sell</span>
              <span className="text-gray-400">{sellMode === "percent" ? "%" : "SOL"}</span>
              <button onClick={toggleSellMode} className="text-gray-400">
                ⇄
              </button>
            </div>
            <div className="text-sm text-gray-400 flex items-center">
              0 CKI · $0 ·{" "}
              <img src={SOL} alt="SOL" className="w-5 h-5 ml-1 mr-1" />
              <span className="text-white">0</span>
            </div>
          </div>
          <div className="grid grid-cols-4 gap-2 mb-3">
  {(sellMode === "percent"
    ? ["10%", "25%", "50%", "100%"]
    : ["0.01", "0.1", "1", "10"]
  ).map((val) => (
    <button
      key={val}
      className="border border-rose-500 rounded-full py-2 text-rose-500 text-center bg-transparent"
    >
      {val}
    </button>
  ))}
</div>


          <TradeStats {...sellSettings} />
        </div>
      </div>
    </div>
  );
};

// Shared TradeStats component
interface TradeStatsProps {
  slippage?: number;
  priority?: number;
  bribe?: number;
  mevMode?: string;
}

const TradeStats = ({ slippage, priority, bribe, mevMode }: TradeStatsProps) => {
  const renderShieldIcon = () => {
    switch (mevMode) {
      case 'Red.':
        return <Shield size={16} className="" />;
      case 'Sec.':
        return <ShieldCheckIcon size={16} className="" />;
      case 'Off':
      default:
        return <ShieldOff size={16} className="" />;
    }
  };
  const Oval = ({ 
    size = 16, 
    color = "#FFEB3B", 
    strokeWidth = 2,
    rx = 10,
    ry = 6 
  }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      {/* Horizontal oval */}
      <ellipse 
        cx="12" 
        cy="12" 
        rx={rx} 
        ry={ry} 
        stroke={color} 
        strokeWidth={strokeWidth}
      />
      
      {/* Curved line through the middle - curved in opposite direction */}
      <path 
        d="M 4 12 Q 12 16 20 12" 
        stroke={color} 
        strokeWidth={strokeWidth}
        fill="none"
      />
    </svg>
  )
  return (
    <div className="flex items-center space-x-4 text-sm mb-2">
      <div className="flex items-center space-x-1">
        <TrendingUp size={16} className="text-gray-400" />
        <span className="text-white">{slippage}%</span>
      </div>
      <div className="flex items-center space-x-1">
        <Fuel size={14} className="text-yellow-500" />
        <span className="text-yellow-500">{priority}</span>
      </div>
      <div className="flex items-center space-x-1">
        <Oval />
        <span className="text-yellow-500">{bribe}</span>
      </div>
      <div className="flex items-center space-x-1 text-gray-400">
        {renderShieldIcon()}
        <span>{mevMode}</span>
      </div>
    </div>
  );
};


const TAB_CONFIG = [
  { id: 'trades', label: 'Trades', component: 'Traders' },
  { id: 'positions', label: 'Positions', component: 'Positions' },
  { id: 'orders', label: 'Orders', component: 'Orders' },
  { id: 'holders', label: 'Holders', component: 'Holders' },
  { id: 'top-traders', label: 'Top Traders', component: 'Top Traders' },
  { id: 'dev-tokens', label: 'Dev Tokens', component: 'Dev Tokens' },
];

interface TablesProps {
  isTradesTableVisible?: boolean;
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
  holders?: any[];
  topTrades?: any[];
  isTokenInfoLoading?: boolean;
}

export default function Tables({ isTradesTableVisible = false, tradeData, holders, topTrades, isTokenInfoLoading }: TablesProps) {
  const [activeTab, setActiveTab] = useState('trades');
  const [showInstantModal, setShowInstantModal] = useState(false);
  const [tradeSubTab, setTradeSubTab] = useState<'dev' | 'you'>('dev');
  const [manualTabChange, setManualTabChange] = useState(false);

  // Use shared trade data from parent component (PulseTrade)
  // This ensures both side panel and bottom table use the same data source

  // This will be replaced by getAvailableTabs() function

  // Reset manual tab change flag
  useEffect(() => {
    if (manualTabChange) {
      setManualTabChange(false);
    }
  }, [manualTabChange]);

  const renderTabContent = () => {
    const activeConfig = TAB_CONFIG.find(tab => tab.id === activeTab);

    if (!activeConfig) {
      return (
        <div className="flex items-center justify-center py-12">
          <p className="text-neutral-400 text-sm">Invalid tab selection: {activeTab}</p>
        </div>
      );
    }

    switch (activeConfig.component) {
      case 'Traders':
        return <Traders view={tradeSubTab} tradeData={tradeData} />;
      case 'Orders':
        return <Orders />;
      case 'Dev Tokens':
        return <DevTokens />;
      case 'Top Traders':
        return <Top_Traders topTrades={topTrades} isLoading={isTokenInfoLoading} />;
      case 'Holders':
        return <Holders holders={holders} isLoading={isTokenInfoLoading} />;
      case 'Positions':
        return <Positions />;
      default:
        return (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <p className="text-neutral-400 text-sm mb-2">
                {activeConfig.label} data coming soon
              </p>
              <p className="text-neutral-500 text-xs">
                This feature is currently under development
              </p>
            </div>
          </div>
        );
    }
  };

  const handleTabChange = (tabId: string) => {
    setManualTabChange(true); // Mark as manual change
    setActiveTab(tabId);
  };

 
  // Filter available tabs based on side panel state
  const getAvailableTabs = () => {
    if (isTradesTableVisible) {
      // When side panel is open, exclude the trades tab
      return TAB_CONFIG.filter(tab => tab.id !== 'trades');
    }
    return TAB_CONFIG;
  };

  const availableTabsFiltered = getAvailableTabs();

  // Auto-switch to first available tab if current tab is not available
  useEffect(() => {
    if (isTradesTableVisible && activeTab === 'trades' && availableTabsFiltered.length > 0) {
      setActiveTab(availableTabsFiltered[0].id);
    }
  }, [isTradesTableVisible, activeTab, availableTabsFiltered]);

  return (
    <>
    <PresetProvider>
      <div className="text-white w-full h-full flex flex-col">
        <div className="container mx-auto flex flex-col h-full px-2">
          {/* Header Section */}
          <header className="border-b border-neutral-800">
            <div className="flex items-center justify-between">
              {/* Navigation Tabs */}
              <nav className="flex space-x-6 overflow-x-auto" role="tablist">
                {availableTabsFiltered.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={`
                      relative text-sm font-medium px-2 py-3 whitespace-nowrap
                      transition-colors duration-200 ease-in-out
                      ${
                        activeTab === tab.id
                          ? 'text-white after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-white'
                          : 'text-neutral-400 hover:text-neutral-200'
                      }
                    `}
                    role="tab"
                    aria-selected={activeTab === tab.id}
                    aria-controls={`tabpanel-${tab.id}`}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>

              {/* Action Controls */}
              <div className="flex items-center space-x-3">
                <div className="flex bg-[#0F1011] rounded-full overflow-hidden">
                  <button
                    onClick={() => setShowInstantModal(true)}
                    className={`flex items-center gap-1
                      text-blue-400 text-sm font-medium
                      hover:text-blue-300 transition-colors duration-200
                      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded
                      p-2`}
                  >
                    Instant Trade
                  </button>
                </div>

                {activeTab === 'trades' && (
                  <>
<button
  onClick={() => setTradeSubTab(tradeSubTab === 'you' ? 'dev' : 'you')}
  className={`flex items-center gap-1 text-sm font-medium transition-colors duration-200 rounded p-1 ${
    tradeSubTab === 'you'
      ? 'text-white bg-neutral-700' // active state with background
      : 'text-[#BBBBBB] hover:text-white'
  }`}
>
  <User size={14} />
  YOU
</button>
                  </>
                )}

                <button
                  className="text-neutral-500 hover:text-neutral-400 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:ring-opacity-50 rounded p-1"
                  aria-label="Refresh data"
                >
                  <RefreshCw size={16} />
                </button>
              </div>
            </div>
          </header>

          {/* Main Content Area */}
          <main className="flex-1 overflow-hidden">
            <div
              role="tabpanel"
              id={`tabpanel-${activeTab}`}
              aria-labelledby={`tab-${activeTab}`}
            >
              {renderTabContent()}
            </div>
          </main>
        </div>
      </div>

      {/* Modal rendered outside the main container using createPortal */}
      {showInstantModal &&
        createPortal(
          <TradingInterface onClose={() => setShowInstantModal(false)} />,
          document.body
        )
      }
      </PresetProvider>
    </>
  );
}