import { useState, useMemo, useEffect } from 'react';
import { ChevronUp, ChevronDown, ExternalLink } from 'lucide-react';
import { formatSmallNumber } from '@/utils/numberFormatting';
import { useUserTradeData } from '@/hooks/useUserTradeData';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { getDefaultSolanaWalletAddress } from '@/utils/walletUtils';

interface TradersProps {
  view?: 'dev' | 'you';
  tradeData?: {
    trades: any[];
    isLoading: boolean;
    isConnected: boolean;
    error: string | null;
    lastUpdate: number | null;
    latestRawTrade: any;
  };
}

const Traders: React.FC<TradersProps> = ({ view, tradeData }) => {
  const [sortAsc, setSortAsc] = useState(false); // Default to newest first
  const [currentTime, setCurrentTime] = useState(Date.now());
  const { user, authenticated } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  
  // Get Solana wallet address instead of Ethereum wallet address
  const solanaWalletAddress = useMemo(() => {
    // First try the default wallet from localStorage
    const defaultAddress = getDefaultSolanaWalletAddress(authenticated, solanaWallets);
    if (defaultAddress) return defaultAddress;
    
    // Fallback to the first Solana wallet if available
    if (solanaWallets && solanaWallets.length > 0 && solanaWallets[0].address) {
      return solanaWallets[0].address;
    }
    
    // If no specific Solana wallet found, check user's linked accounts
    if (user?.linkedAccounts) {
      const solanaAccount = user.linkedAccounts.find((account) => 
        account.type === 'wallet' && 
        'address' in account && 
        typeof account.address === 'string' && 
        !account.address.startsWith('0x')
      );
      
      if (solanaAccount && 'address' in solanaAccount && typeof solanaAccount.address === 'string') {
        return solanaAccount.address;
      }
    }
    
    return null;
  }, [user, authenticated, solanaWallets]);
  
  // Use user-specific trade data for 'you' view with Solana wallet address
  const userTradeData = useUserTradeData(view === 'you' ? solanaWalletAddress : null);

  // Use trade data from props (passed from Tables component)
  const { trades = [], isLoading = false, isConnected = false, error = null } = tradeData || {};

  // Real-time age updates (similar to side panel implementation)
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000); // Update every second

    return () => clearInterval(interval);
  }, []);

  // Format age with real-time updates (matching side panel implementation)
  const formatRealTimeAge = (timestamp: number): string => {
    // Handle invalid timestamps - show "just now" instead of "unknown"
    if (!timestamp || isNaN(timestamp) || timestamp <= 0) {
      console.warn('⚠️ Invalid timestamp in formatRealTimeAge:', timestamp);
      return 'just now';
    }

    const diffSeconds = Math.floor((currentTime - timestamp) / 1000);

    // Handle negative or invalid time differences - show "just now" instead of "unknown"
    if (isNaN(diffSeconds) || diffSeconds < 0) {
      console.warn('⚠️ Invalid time difference:', { currentTime, timestamp, diffSeconds });
      return 'just now';
    }

    if (diffSeconds < 1) {
      return 'just now';
    } else if (diffSeconds < 60) {
      return `${diffSeconds}s ago`;
    } else if (diffSeconds < 3600) {
      return `${Math.floor(diffSeconds / 60)}m ago`;
    } else if (diffSeconds < 86400) {
      return `${Math.floor(diffSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffSeconds / 86400)}d ago`;
    }
  };

  // Format market cap from trade data (matching trade_example.md logic)
  const formatMarketCap = (trade: any): string => {
    let marketCap = trade.marketCap || 0;

    if (marketCap >= 1e12) {
      return (marketCap / 1e12).toFixed(2) + 'T';
    } else if (marketCap >= 1e9) {
      return (marketCap / 1e9).toFixed(2) + 'B';
    } else if (marketCap >= 1e6) {
      return (marketCap / 1e6).toFixed(2) + 'M';
    } else if (marketCap >= 1e3) {
      return (marketCap / 1e3).toFixed(2) + 'K';
    } else {
      return marketCap.toFixed(0);
    }
  };

  // Format token amount from trade data (using actualTokenAmount for actual token quantity)
  const formatTokenAmount = (trade: any): string => {
    // Use actualTokenAmount from FormattedTrade (actual token quantity being traded)
    let tokenAmount = trade.actualTokenAmount || 0;

    let formattedAmount = '';
    if (tokenAmount >= 1e12) {
      formattedAmount = (tokenAmount / 1e12).toFixed(3) + 'T';
    } else if (tokenAmount >= 1e9) {
      formattedAmount = (tokenAmount / 1e9).toFixed(3) + 'B';
    } else if (tokenAmount >= 1e6) {
      formattedAmount = (tokenAmount / 1e6).toFixed(3) + 'M';
    } else if (tokenAmount >= 1e3) {
      formattedAmount = (tokenAmount / 1e3).toFixed(3) + 'K';
    } else {
      // For amounts less than 1000, show with 3 decimal places and add thousand separators
      formattedAmount = tokenAmount.toLocaleString('en-US', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
      });
    }

    // Return just the formatted amount without token symbol
    return formattedAmount;
  };

  // Format SOL amount from trade data (using token_amount_vs for SOL equivalent)
  const formatSOLAmount = (trade: any): string => {
    // Use tokenAmount which should contain token_amount_vs (SOL amount) from the formatted trade
    let solAmount = trade.tokenAmount || 0;

    // For very small amounts, use scientific notation
    if (solAmount < 0.000001 && solAmount > 0) {
      return solAmount.toExponential(2);
    }

    let formattedAmount = '';
    if (solAmount >= 1e12) {
      formattedAmount = (solAmount / 1e12).toFixed(3) + 'T';
    } else if (solAmount >= 1e9) {
      formattedAmount = (solAmount / 1e9).toFixed(3) + 'B';
    } else if (solAmount >= 1e6) {
      formattedAmount = (solAmount / 1e6).toFixed(3) + 'M';
    } else if (solAmount >= 1e3) {
      formattedAmount = (solAmount / 1e3).toFixed(3) + 'K';
    } else if (solAmount < 0.0001) {
      // For very small amounts, show more decimal places
      formattedAmount = solAmount.toFixed(8);
    } else if (solAmount < 0.01) {
      // For small amounts, show more decimal places
      formattedAmount = solAmount.toFixed(6);
    } else if (solAmount < 1) {
      // For medium amounts, show fewer decimal places
      formattedAmount = solAmount.toFixed(4);
    } else {
      // For larger amounts, show 2 decimal places
      formattedAmount = solAmount.toFixed(2);
    }

    // Return just the formatted amount without SOL text
    return formattedAmount;
  };

  // Format trade USD value (keeping for reference, but will be replaced with SOL)
  const formatTradeValue = (trade: any): string => {
    let usdValue = trade.tokenAmountUsd || 0;
    return formatSmallNumber(usdValue);
  };

  // Format transaction hash (return just the shortened hash)
  const formatHash = (trade: any): string => {
    let hash = trade.txHash || '';

    if (!hash || hash === 'N/A') {
      return 'N/A';
    }

    if (hash.length > 8) {
      return `${hash.substring(0, 4)}...${hash.substring(hash.length - 4)}`;
    }

    return hash;
  };

  // Get full hash for click handler
  const getFullHash = (trade: any): string => {
    return trade.txHash || '';
  };

  // Handle hash click to open Solscan
  const handleHashClick = (hash: string) => {
    if (hash && hash !== 'N/A') {
      const explorerUrl = `https://solscan.io/tx/${hash}`;
      window.open(explorerUrl, '_blank', 'noopener,noreferrer');
    }
  };

  const getCurrentData = () => {
    // If in 'you' view, use data from useUserTradeData hook
    if (view === 'you') {
      return [...userTradeData.trades].sort((a, b) => {
        return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
      });
    }
    
    // If in 'dev' view, would filter for dev wallets (not implemented yet)
    if (view === 'dev') {
      // TODO: Implement proper dev wallet identification
      return [...trades].sort((a, b) => {
        return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
      });
    }

    // Default view - all trades
    return [...trades].sort((a, b) => {
      return sortAsc ? a.timestamp - b.timestamp : b.timestamp - a.timestamp;
    });
  };

  // Determine loading and error states based on current view
  const isViewLoading = view === 'you' ? userTradeData.isLoading : isLoading;
  const viewError = view === 'you' ? userTradeData.error : error;
  const currentData = getCurrentData();
  const currentDataLength = currentData ? currentData.length : 0;

  // ---------- UI ----------
  return (
    <div className="rounded-lg overflow-hidden">
      {/* Connection Status */}
      <div className="flex items-center justify-between px-6 py-2 bg-neutral-900/40 border-b border-neutral-700">
        <div className="flex items-center space-x-2 text-xs">
          <div className={`w-2 h-2 rounded-full ${(view === 'you' ? true : isConnected) ? 'bg-green-400' : 'bg-red-400'}`}></div>
          <span className="text-neutral-400">
            {isViewLoading 
              ? 'Loading trades...' 
              : view === 'you'
                ? `Your Trades • ${currentDataLength} trades` 
                : isConnected 
                  ? `Live • ${trades.length} trades` 
                  : 'Disconnected'
            }
          </span>
          {viewError && <span className="text-red-400">Error: {viewError}</span>}
        </div>
        <div className="text-xs text-neutral-500">
          {currentDataLength > 0 ? `${currentDataLength} trades` : 'No trades available'}
        </div>
      </div>

      {/* Table Structure with Fixed Height and Scroll */}
      <div className="flex flex-col h-[600px]"> {/* Increased height container */}
        {/* Fixed Header */}
        <div className="flex-shrink-0 border-b border-neutral-700 bg-[#141416]">
          <div className="flex text-neutral-400 text-xs">
            <div className="flex-1 px-4 py-3">
              <div
                className="flex items-center cursor-pointer hover:text-white transition-colors"
                onClick={() => setSortAsc(!sortAsc)}
              >
                <span>{view === 'you' ? 'Date' : 'Age'}</span>
                {sortAsc ? <ChevronUp size={12} className="ml-1" /> : <ChevronDown size={12} className="ml-1" />}
              </div>
            </div>
            <div className="flex-1 px-4 py-3">Type</div>
            {view !== 'you' && <div className="flex-1 px-4 py-3">Market Cap</div>}
            <div className="flex-1 px-4 py-3">Amount</div>
            <div className="flex-1 px-4 py-3">Total SOL</div>
            <div className="flex-1 px-4 py-3">Hash</div>
          </div>
        </div>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="divide-y divide-neutral-800">
            {isViewLoading && (
              <div className="text-center py-8">
                <div className="text-neutral-400">
                  {view === 'you' ? 'Loading your trades...' : 'Loading trade data...'}
                </div>
              </div>
            )}

            {!isViewLoading && currentDataLength === 0 && (
              <div className="text-center py-8">
                <div className="text-neutral-400">
                  {viewError 
                    ? 'Failed to load trade data' 
                    : view === 'you' && !solanaWalletAddress
                      ? 'Connect your Solana wallet to view your trades'
                      : view === 'you'
                        ? 'No trades found for your wallet' 
                        : 'No trades available'
                  }
                </div>
                {view === 'you' && solanaWalletAddress && (
                  <div className="text-xs text-blue-400 mt-2">
                    Checking trades for wallet: {solanaWalletAddress.slice(0, 6)}...{solanaWalletAddress.slice(-4)}
                  </div>
                )}
              </div>
            )}

            {!isViewLoading && getCurrentData().map((trade, index) => (
              <div key={trade.id || index} className="flex hover:bg-neutral-800/50 transition-colors">
                <div className="flex-1 px-4 py-3 text-neutral-500 text-sm">
                  {view === 'you' 
                    ? new Date(trade.timestamp).toLocaleDateString() 
                    : formatRealTimeAge(trade.timestamp)}
                </div>
                <div className={`flex-1 px-4 py-3 text-sm ${trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}`}>
                  {trade.type === 'buy' ? 'BUY' : 'SELL'}
                </div>
                {view !== 'you' && (
                  <div className="flex-1 px-4 py-3 text-white text-sm">
                    {formatMarketCap(trade)}
                  </div>
                )}
                <div className="flex-1 px-4 py-3 text-white text-sm">
                  {formatTokenAmount(trade)}
                </div>
                <div className="flex-1 px-4 py-3 text-white text-sm">
                  <div className="flex items-center">
                    <img
                      src="https://metacore.mobula.io/78ee4d656f4f152a90d733f4eaaa4e1685e25bc654087acdb62bfe494d668976.png"
                      alt="SOL"
                      className="w-3 h-3 mr-1"
                    />
                    <span className={trade.type === 'buy' ? 'text-green-400' : 'text-red-400'}>
                      {formatSOLAmount(trade)}
                    </span>
                  </div>
                </div>
                <div className="flex-1 px-4 py-3 text-sm flex items-center">
                  <span 
                    className="text-blue-400 hover:text-blue-300 transition-colors cursor-pointer"
                    onClick={() => handleHashClick(getFullHash(trade))}
                  >
                    {formatHash(trade)}
                  </span>
                  <ExternalLink size={12} className="ml-1 text-neutral-500" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Traders;
