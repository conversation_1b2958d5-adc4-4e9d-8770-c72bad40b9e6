import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface ActiveToken {
  id: string;
  address: string;
  symbol: string;
  name: string;
  imageUrl?: string;
  price: number;
  market_cap?: number;
  pool_address?: string;
  exchange_name?: string;
  network?: string;
  balance?: string;
  decimals?: number;
}

interface ActiveTokenContextType {
  activeToken: ActiveToken | null;
  setActiveToken: (token: ActiveToken | null) => void;
}

const ActiveTokenContext = createContext<ActiveTokenContextType | undefined>(undefined);

export const useActiveToken = () => {
  const context = useContext(ActiveTokenContext);
  if (context === undefined) {
    throw new Error('useActiveToken must be used within an ActiveTokenProvider');
  }
  return context;
};

interface ActiveTokenProviderProps {
  children: ReactNode;
}

export const ActiveTokenProvider: React.FC<ActiveTokenProviderProps> = ({ children }) => {
  const [activeToken, setActiveTokenState] = useState<ActiveToken | null>(null);

  // Load active token from localStorage on mount
  useEffect(() => {
    const storedToken = localStorage.getItem('activePulseToken');
    if (storedToken) {
      try {
        const parsedToken = JSON.parse(storedToken);
        setActiveTokenState(parsedToken);
      } catch (error) {
        console.error('Error parsing stored active token:', error);
      }
    }
  }, []);

  // Listen for changes to activePulseToken in localStorage
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'activePulseToken') {
        if (e.newValue) {
          try {
            const parsedToken = JSON.parse(e.newValue);
            setActiveTokenState(parsedToken);
          } catch (error) {
            console.error('Error parsing updated active token:', error);
          }
        } else {
          setActiveTokenState(null);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // Listen for custom events from other components
  useEffect(() => {
    const handlePulseDataChange = (event: CustomEvent) => {
      if (event.detail?.activePulseToken) {
        setActiveTokenState(event.detail.activePulseToken);
      }
    };

    window.addEventListener('pulseDataChanged', handlePulseDataChange as EventListener);
    return () => window.removeEventListener('pulseDataChanged', handlePulseDataChange as EventListener);
  }, []);

  const setActiveToken = (token: ActiveToken | null) => {
    setActiveTokenState(token);
    if (token) {
      localStorage.setItem('activePulseToken', JSON.stringify(token));
    } else {
      localStorage.removeItem('activePulseToken');
    }
  };

  const value = {
    activeToken,
    setActiveToken,
  };

  return (
    <ActiveTokenContext.Provider value={value}>
      {children}
    </ActiveTokenContext.Provider>
  );
};

export default ActiveTokenContext;