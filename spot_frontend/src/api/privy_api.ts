import axios from 'axios';

// Get the backend API URL from environment variables with dynamic fallback
function getBackendApiUrl() {
  // Always use the proxy path for all environments
  return '/api';
}

const BACKEND_API_URL = getBackendApiUrl();

// TypeScript interfaces for API requests and responses
export interface PrivySolanaWalletRequest {
  userId: string;
}

export interface PrivySolanaWalletResponse {
  success: boolean;
  data?: {
    walletId: string;
    address: string;
    verifiedAt: string;
    totalSolanaWallets: number;
    allSolanaWallets: Array<{
      id: string;
      address: string;
      verifiedAt: string;
    }>;
  };
  error?: string;
}

export interface UnlinkWalletRequest {
  userId: string;
  walletAddress: string;
  walletType?: string;
}

export interface UnlinkWalletResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Cache key for storing wallet information in localStorage
 */
const WALLET_CACHE_KEY = 'privy_solana_wallet_cache';
const CACHE_EXPIRY_HOURS = 24; // Cache for 24 hours

interface CachedWalletInfo {
  walletId: string;
  address: string;
  verifiedAt: string;
  cachedAt: number;
  userId: string;
}

/**
 * Get cached wallet information from localStorage
 */
const getCachedWalletInfo = (userId: string): CachedWalletInfo | null => {
  try {
    const cached = localStorage.getItem(WALLET_CACHE_KEY);
    if (!cached) return null;

    const cachedData: CachedWalletInfo = JSON.parse(cached);
    
    // Check if cache is for the same user
    if (cachedData.userId !== userId) {
      localStorage.removeItem(WALLET_CACHE_KEY);
      return null;
    }

    // Check if cache is expired
    const now = Date.now();
    const cacheAge = now - cachedData.cachedAt;
    const maxAge = CACHE_EXPIRY_HOURS * 60 * 60 * 1000; // Convert hours to milliseconds

    if (cacheAge > maxAge) {
      localStorage.removeItem(WALLET_CACHE_KEY);
      return null;
    }

    return cachedData;
  } catch (error) {
    console.error('Error reading wallet cache:', error);
    localStorage.removeItem(WALLET_CACHE_KEY);
    return null;
  }
};

/**
 * Cache wallet information in localStorage
 */
const setCachedWalletInfo = (userId: string, walletId: string, address: string, verifiedAt: string) => {
  try {
    const cacheData: CachedWalletInfo = {
      walletId,
      address,
      verifiedAt,
      cachedAt: Date.now(),
      userId
    };

    localStorage.setItem(WALLET_CACHE_KEY, JSON.stringify(cacheData));
    console.log('Cached wallet info for user:', userId);
  } catch (error) {
    console.error('Error caching wallet info:', error);
  }
};

/**
 * Clear cached wallet information
 */
export const clearWalletCache = () => {
  localStorage.removeItem(WALLET_CACHE_KEY);
  console.log('Wallet cache cleared');
};

/**
 * Get clean user ID (remove 'did:privy:' prefix if present)
 */
const getCleanUserId = (userId: string): string => {
  if (userId.startsWith('did:privy:')) {
    return userId.replace('did:privy:', '');
  }
  return userId;
};

/**
 * Get Solana wallet information from Privy API via backend
 * This function implements caching and fallback mechanisms
 */
export const getPrivySolanaWalletInfo = async (
  userId: string,
  forceRefresh: boolean = false
): Promise<PrivySolanaWalletResponse> => {
  try {
    // Clean the user ID (remove 'did:privy:' prefix if present)
    const cleanUserId = getCleanUserId(userId);

    // Check cache first (unless force refresh is requested)
    if (!forceRefresh) {
      const cached = getCachedWalletInfo(cleanUserId);
      if (cached) {
        console.log('Using cached wallet info for user:', cleanUserId);
        return {
          success: true,
          data: {
            walletId: cached.walletId,
            address: cached.address,
            verifiedAt: cached.verifiedAt,
            totalSolanaWallets: 1,
            allSolanaWallets: [{
              id: cached.walletId,
              address: cached.address,
              verifiedAt: cached.verifiedAt
            }]
          }
        };
      }
    }

    console.log('Fetching wallet info from API for user:', cleanUserId);

    // Call backend API with clean user ID
    const response = await axios.post<PrivySolanaWalletResponse>(
      `${BACKEND_API_URL}/wallet/privy-solana-info`,
      { userId: cleanUserId },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    const result = response.data;

    // Cache successful response using clean user ID
    if (result.success && result.data) {
      setCachedWalletInfo(
        cleanUserId,
        result.data.walletId,
        result.data.address,
        result.data.verifiedAt
      );
    }

    return result;

  } catch (error) {
    console.error('Error fetching Privy wallet info:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.error || error.message;

      return {
        success: false,
        error: `API Error (${status}): ${message}`
      };
    }

    return {
      success: false,
      error: 'Network error while fetching wallet information'
    };
  }
};

/**
 * Preload wallet information on login/mount
 * This should be called when user logs in or component mounts
 */
export const preloadWalletInfo = async (userId: string): Promise<void> => {
  try {
    const cleanUserId = getCleanUserId(userId);
    console.log('Preloading wallet info for user:', cleanUserId);

    // Check if we already have fresh cache
    const cached = getCachedWalletInfo(cleanUserId);
    if (cached) {
      console.log('Wallet info already cached, skipping preload');
      return;
    }

    // Fetch and cache wallet info
    await getPrivySolanaWalletInfo(userId, false);
    console.log('Wallet info preloaded successfully');
  } catch (error) {
    console.error('Error preloading wallet info:', error);
    // Don't throw - this is a background operation
  }
};

/**
 * Validate if a wallet address matches the cached/expected address
 * This helps ensure consistency between connected wallet and Privy records
 */
export const validateWalletAddress = (userId: string, connectedAddress: string): boolean => {
  const cleanUserId = getCleanUserId(userId);
  const cached = getCachedWalletInfo(cleanUserId);
  if (!cached) return true; // No cache to validate against

  return cached.address === connectedAddress;
};

/**
 * Get wallet ID for a specific address (useful when user has multiple Solana wallets)
 */
export const getWalletIdForAddress = async (
  userId: string,
  targetAddress: string
): Promise<string | null> => {
  try {
    const response = await getPrivySolanaWalletInfo(userId);

    if (!response.success || !response.data) {
      return null;
    }

    // Find wallet with matching address
    const matchingWallet = response.data.allSolanaWallets.find(
      wallet => wallet.address === targetAddress
    );

    return matchingWallet?.id || null;
  } catch (error) {
    console.error('Error finding wallet ID for address:', error);
    return null;
  }
};

/**
 * Get cached wallet ID directly (for swap operations)
 * Returns null if not cached - use this for performance-critical operations
 */
export const getCachedWalletId = (userId: string, walletAddress: string): string | null => {
  const cleanUserId = getCleanUserId(userId);
  const cached = getCachedWalletInfo(cleanUserId);

  if (!cached) {
    console.log('No cached wallet info found for user:', cleanUserId);
    return null;
  }

  if (cached.address !== walletAddress) {
    console.warn('Cached wallet address mismatch:', {
      cached: cached.address,
      connected: walletAddress
    });
    return null;
  }

  return cached.walletId;
};

/**
 * Unlink a specific wallet from a Privy user
 * Uses the backend API to securely call Privy's REST API
 */
export const unlinkPrivyWallet = async (
  userId: string,
  walletAddress: string,
  walletType?: string
): Promise<UnlinkWalletResponse> => {
  try {
    // Clean the user ID (remove 'did:privy:' prefix if present)
    const cleanUserId = getCleanUserId(userId);

    console.log('Unlinking wallet via backend API:', {
      userId: cleanUserId,
      walletAddress,
      walletType
    });

    // Call backend API to unlink wallet
    const response = await axios.post<UnlinkWalletResponse>(
      `${BACKEND_API_URL}/wallet/unlink-wallet`,
      {
        userId: cleanUserId,
        walletAddress,
        walletType: walletType || 'wallet'
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    const result = response.data;

    // Clear cache if wallet was successfully unlinked
    if (result.success) {
      const cached = getCachedWalletInfo(cleanUserId);
      if (cached && cached.address === walletAddress) {
        clearWalletCache();
        console.log('Cleared wallet cache after successful unlink');
      }
    }

    return result;

  } catch (error) {
    console.error('Error unlinking wallet:', error);

    if (axios.isAxiosError(error)) {
      const status = error.response?.status || 500;
      const message = error.response?.data?.error || error.message;

      return {
        success: false,
        error: message
      };
    }

    return {
      success: false,
      error: 'Network error occurred while unlinking wallet'
    };
  }
};
