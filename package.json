{"name": "redfyn-spot", "version": "1.0.0", "description": "Multi-Chain DEX and Wallet Management Platform", "private": true, "scripts": {"dev": "concurrently --kill-others --prefix-colors \"cyan,magenta,yellow,green,blue,red\" --prefix \"[{name}]\" \"npm run dev:frontend\" \"npm run dev:spot-backend\" \"npm run dev:solana\" \"npm run dev:trading-panel\" \"npm run dev:limit-orders\"", "dev:frontend": "cd spot_frontend && npm run dev", "dev:spot-backend": "cd backend/spot_backend && npm run dev", "dev:solana": "cd backend/solana && npm run dev", "dev:trading-panel": "cd backend/trading_panel && npm run dev", "dev:limit-orders": "cd backend/limit_orders && npm run dev", "install:all": "npm install && npm run install:frontend && npm run install:backends", "install:frontend": "cd spot_frontend && npm install", "install:backends": "npm run install:spot-backend && npm run install:solana && npm run install:trading-panel && npm run install:limit-orders", "install:spot-backend": "cd backend/spot_backend && npm install", "install:solana": "cd backend/solana && npm install", "install:trading-panel": "cd backend/trading_panel && npm install", "install:limit-orders": "cd backend/limit_orders && npm install", "build:all": "npm run build:backends && npm run build:frontend", "build:frontend": "cd spot_frontend && npm run build", "build:backends": "npm run build:spot-backend && npm run build:solana && npm run build:trading-panel && npm run build:limit-orders", "build:spot-backend": "cd backend/spot_backend && npm run build", "build:solana": "cd backend/solana && npm run build", "build:trading-panel": "cd backend/trading_panel && npm run build", "build:limit-orders": "cd backend/limit_orders && npm run build", "start:all": "concurrently --kill-others --prefix-colors \"cyan,magenta,yellow,green,blue,red\" --prefix \"[{name}]\" \"npm run start:frontend\" \"npm run start:spot-backend\" \"npm run start:solana\" \"npm run start:trading-panel\" \"npm run start:limit-orders\"", "start:frontend": "cd spot_frontend && npm start", "start:spot-backend": "cd backend/spot_backend && npm start", "start:solana": "cd backend/solana && npm start", "start:trading-panel": "cd backend/trading_panel && npm start", "start:limit-orders": "cd backend/limit_orders && npm start", "test:all": "npm run test:frontend && npm run test:backends", "test:frontend": "cd spot_frontend && npm test", "test:backends": "npm run test:spot-backend && npm run test:solana", "test:spot-backend": "cd backend/spot_backend && npm test", "test:solana": "cd backend/solana && npm test", "clean": "npm run clean:frontend && npm run clean:backends && rm -rf node_modules", "clean:frontend": "cd spot_frontend && rm -rf node_modules dist", "clean:backends": "npm run clean:spot-backend && npm run clean:solana && npm run clean:trading-panel && npm run clean:limit-orders", "clean:spot-backend": "cd backend/spot_backend && rm -rf node_modules dist", "clean:solana": "cd backend/solana && rm -rf node_modules dist", "clean:trading-panel": "cd backend/trading_panel && rm -rf node_modules dist", "clean:limit-orders": "cd backend/limit_orders && rm -rf node_modules dist", "ports": "./scripts/check-ports.sh", "docker:dev": "docker-compose up --build", "docker:prod": "docker-compose -f docker-compose.prod.yml up --build -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "lint:all": "npm run lint:frontend && npm run lint:backends", "lint:frontend": "cd spot_frontend && npm run lint", "lint:backends": "npm run lint:spot-backend && npm run lint:solana", "lint:spot-backend": "cd backend/spot_backend && npm run lint", "lint:solana": "cd backend/solana && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2", "npm-run-all": "^4.1.5"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/redfyn-spot.git"}, "keywords": ["defi", "dex", "cryptocurrency", "trading", "multi-chain", "solana", "ethereum", "liquidity-pool"], "author": "RedFyn Team", "license": "MIT", "bugs": {"url": "https://github.com/your-org/redfyn-spot/issues"}, "homepage": "https://github.com/your-org/redfyn-spot#readme", "dependencies": {"ws": "^8.18.2"}}