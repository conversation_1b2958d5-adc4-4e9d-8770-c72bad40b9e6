#!/usr/bin/env node

/**
 * WebSocket Connection Lifecycle Test Script
 * 
 * This script tests the enhanced WebSocket resource management by simulating
 * client connections and disconnections to verify proper cleanup behavior.
 */

const WebSocket = require('ws');
const axios = require('axios');

// Configuration
const CONFIG = {
  // Backend services
  SPOT_BACKEND_WS: 'ws://localhost:4001/socket.io',
  TRADING_PANEL_WS: 'ws://localhost:4001/trading-panel-ws',
  
  // Test parameters
  TEST_POOL_ADDRESS: 'So11111111111111111111111111111111111111112', // SOL address for testing
  CLIENT_COUNT: 3,
  DISCONNECT_DELAY: 5000, // 5 seconds
  CLEANUP_WAIT_TIME: 35000, // 35 seconds (longer than 30s timeout)
  
  // Health check endpoints
  SPOT_BACKEND_HEALTH: 'http://localhost:4001/api/health',
  TRADING_PANEL_HEALTH: 'http://localhost:5003/status'
};

class WebSocketLifecycleTest {
  constructor() {
    this.clients = [];
    this.testResults = {
      connectionTest: false,
      disconnectionTest: false,
      cleanupTest: false,
      reconnectionTest: false
    };
  }

  /**
   * Run the complete test suite
   */
  async runTests() {
    console.log('🧪 Starting WebSocket Connection Lifecycle Tests\n');
    
    try {
      // Test 1: Connection establishment
      await this.testConnectionEstablishment();
      
      // Test 2: Client disconnection
      await this.testClientDisconnection();
      
      // Test 3: Backend cleanup verification
      await this.testBackendCleanup();
      
      // Test 4: Reconnection capability
      await this.testReconnection();
      
      // Summary
      this.printTestSummary();
      
    } catch (error) {
      console.error('❌ Test suite failed:', error.message);
      process.exit(1);
    }
  }

  /**
   * Test 1: Connection Establishment
   */
  async testConnectionEstablishment() {
    console.log('📡 Test 1: Connection Establishment');
    
    try {
      // Connect multiple clients to trading panel
      for (let i = 0; i < CONFIG.CLIENT_COUNT; i++) {
        const client = await this.connectTradingClient(i);
        this.clients.push(client);
        await this.delay(1000); // 1 second between connections
      }
      
      console.log(`✅ Successfully connected ${CONFIG.CLIENT_COUNT} clients`);
      
      // Verify backend status
      const status = await this.checkBackendStatus();
      console.log('📊 Backend Status:', status);
      
      this.testResults.connectionTest = true;
      
    } catch (error) {
      console.error('❌ Connection test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  /**
   * Test 2: Client Disconnection
   */
  async testClientDisconnection() {
    console.log('🔌 Test 2: Client Disconnection');
    
    try {
      // Disconnect all clients
      console.log(`Disconnecting ${this.clients.length} clients...`);
      
      this.clients.forEach((client, index) => {
        client.close();
        console.log(`🔌 Client ${index} disconnected`);
      });
      
      this.clients = [];
      console.log('✅ All clients disconnected');
      
      this.testResults.disconnectionTest = true;
      
    } catch (error) {
      console.error('❌ Disconnection test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  /**
   * Test 3: Backend Cleanup Verification
   */
  async testBackendCleanup() {
    console.log('🧹 Test 3: Backend Cleanup Verification');
    console.log(`Waiting ${CONFIG.CLEANUP_WAIT_TIME / 1000}s for cleanup to trigger...`);
    
    try {
      // Wait for cleanup timeout
      await this.delay(CONFIG.CLEANUP_WAIT_TIME);
      
      // Check backend status after cleanup
      const statusAfterCleanup = await this.checkBackendStatus();
      console.log('📊 Backend Status After Cleanup:', statusAfterCleanup);
      
      // Verify cleanup occurred
      const cleanupOccurred = this.verifyCleanupOccurred(statusAfterCleanup);
      
      if (cleanupOccurred) {
        console.log('✅ Backend cleanup verified - resources optimized');
        this.testResults.cleanupTest = true;
      } else {
        console.log('⚠️ Backend cleanup may not have occurred as expected');
      }
      
    } catch (error) {
      console.error('❌ Cleanup verification failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  /**
   * Test 4: Reconnection Capability
   */
  async testReconnection() {
    console.log('🔄 Test 4: Reconnection Capability');
    
    try {
      // Reconnect a single client
      console.log('Reconnecting client to verify automatic backend restoration...');
      
      const client = await this.connectTradingClient(0);
      this.clients.push(client);
      
      // Wait a moment for backend to restore
      await this.delay(3000);
      
      // Verify backend is active again
      const statusAfterReconnect = await this.checkBackendStatus();
      console.log('📊 Backend Status After Reconnection:', statusAfterReconnect);
      
      console.log('✅ Reconnection test completed');
      this.testResults.reconnectionTest = true;
      
      // Clean up
      client.close();
      this.clients = [];
      
    } catch (error) {
      console.error('❌ Reconnection test failed:', error.message);
      throw error;
    }
    
    console.log('');
  }

  /**
   * Connect a trading panel client
   */
  async connectTradingClient(clientId) {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(CONFIG.TRADING_PANEL_WS);
      
      ws.on('open', () => {
        console.log(`🔌 Client ${clientId} connected to trading panel`);
        
        // Subscribe to test pool
        const subscribeMessage = {
          action: 'subscribe',
          poolAddress: CONFIG.TEST_POOL_ADDRESS
        };
        
        ws.send(JSON.stringify(subscribeMessage));
        
        // Wait for subscription confirmation
        ws.on('message', (data) => {
          const message = JSON.parse(data.toString());
          if (message.type === 'connected' && message.data.connected) {
            console.log(`✅ Client ${clientId} subscribed to pool`);
            resolve(ws);
          }
        });
      });
      
      ws.on('error', (error) => {
        console.error(`❌ Client ${clientId} connection error:`, error.message);
        reject(error);
      });
      
      // Timeout after 10 seconds
      setTimeout(() => {
        reject(new Error(`Client ${clientId} connection timeout`));
      }, 10000);
    });
  }

  /**
   * Check backend service status
   */
  async checkBackendStatus() {
    try {
      const [spotBackendResponse, tradingPanelResponse] = await Promise.allSettled([
        axios.get(CONFIG.SPOT_BACKEND_HEALTH, { timeout: 5000 }),
        axios.get(CONFIG.TRADING_PANEL_HEALTH, { timeout: 5000 })
      ]);
      
      return {
        spotBackend: spotBackendResponse.status === 'fulfilled' ? spotBackendResponse.value.data : 'Error',
        tradingPanel: tradingPanelResponse.status === 'fulfilled' ? tradingPanelResponse.value.data : 'Error',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.warn('⚠️ Could not fetch backend status:', error.message);
      return { error: error.message, timestamp: new Date().toISOString() };
    }
  }

  /**
   * Verify that cleanup occurred based on status
   */
  verifyCleanupOccurred(status) {
    // This is a simplified check - in a real implementation,
    // you would check specific metrics like active connections,
    // pool counts, etc.
    
    if (status.tradingPanel && typeof status.tradingPanel === 'object') {
      const clientCount = status.tradingPanel.clientCount || 0;
      const poolCount = status.tradingPanel.poolCount || 0;
      
      return clientCount === 0 && poolCount === 0;
    }
    
    // If we can't verify, assume cleanup occurred
    return true;
  }

  /**
   * Print test summary
   */
  printTestSummary() {
    console.log('📋 Test Summary:');
    console.log('================');
    
    Object.entries(this.testResults).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSED' : '❌ FAILED';
      console.log(`${test}: ${status}`);
    });
    
    const allPassed = Object.values(this.testResults).every(result => result);
    
    console.log('\n' + '='.repeat(50));
    if (allPassed) {
      console.log('🎉 All tests passed! WebSocket lifecycle management is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please check the implementation.');
    }
    console.log('='.repeat(50));
  }

  /**
   * Utility: Delay function
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const test = new WebSocketLifecycleTest();
  test.runTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = WebSocketLifecycleTest;
