# Mobula WebSocket Trade Feed Integration

## Overview

This document describes the integration with Mobula's WebSocket trade feed, which provides real-time trade data for Solana tokens with rich metadata.

## Mobula WebSocket Data Structure

### Trade Message Format

```json
{
  "pair": "CinCsUWhhcMavXwJpmsLCMNJDPoVFB4AKCor1gzsizJq",
  "date": 1750256288000,
  "token_price": 0.000003434986887366004,
  "token_price_vs": 146.3258563381864,
  "token_amount": 4325719.071081,
  "token_amount_vs": 0.101545883,
  "token_amount_usd": 14.858788287592287,
  "type": "sell",
  "operation": "regular",
  "blockchain": "Solana",
  "hash": "4t9pjPvN5FGsSnkVme12zHn6Vei94vzTz2PcjFSHdb7uHs5x3Psv7JKxRu9GkFYdnTgBSMXEpS2YT3gHF2Ur3JAP",
  "sender": "D2oYJTcRiXeH4TsAWqLkKAvMZvBU6LCHMoLuMNeJB2CR",
  "pairData": {
    "token0": {
      "address": "35F8XXdq7vMMYhDkJKGrrUBtaqRePoar5Ux98QuNpump",
      "price": 0.0000034514093460408278,
      "symbol": "Goody",
      "name": "Geeked Woody",
      "decimals": 6,
      "marketCap": 3451.4093460408276,
      "logo": "https://ipfs.io/ipfs/..."
    },
    "token1": {
      "address": "So11111111111111111111111111111111111111112",
      "price": 146.32310462672905,
      "symbol": "SOL",
      "name": "Solana",
      "decimals": 9,
      "marketCap": 76023812070.95892
    },
    "volume24h": 714317.2435998407,
    "liquidity": 6220.************,
    "exchange": {
      "name": "PumpSwap",
      "logo": "https://statics.solscan.io/..."
    },
    "trades_24h": 6710,
    "buys_24h": 3349,
    "sells_24h": 3361,
    "volume_24h": 827343.8904166116,
    "price_change_24h": -97.87921926553611
  }
}
```

## Backend Integration

### 1. WebSocket Service (`mobulaTradeWebSocketService.ts`)

**Key Features:**
- Real-time WebSocket connection to Mobula
- Automatic reconnection with exponential backoff
- Trade history caching (last 100 trades per pool)
- Subscription management for multiple pools

**Enhanced Data Handling:**
```typescript
interface TradeData {
  pair: string;
  date: number;
  token_price: number;
  token_amount: number;
  token_amount_vs: number; // SOL amount
  token_amount_usd: number;
  type: 'buy' | 'sell';
  hash: string;
  sender: string;
  pairData: {
    token0: TokenInfo;
    token1: TokenInfo;
    volume24h: number;
    liquidity: number;
    exchange: ExchangeInfo;
    // ... more fields
  };
}
```

### 2. Trade Service (`tradeService.ts`)

**Enhanced Data Conversion:**
- Converts Mobula WebSocket format to internal API format
- Adds computed fields (SOL amounts, display values)
- Handles both WebSocket and API data sources

**New Fields Added:**
```typescript
interface TradeData {
  // ... existing fields
  sol_amount?: number;
  token_symbol?: string;
  token_name?: string;
  exchange?: string;
  market_cap?: number;
  liquidity?: number;
  volume_24h?: number;
  price_change_24h?: number;
}
```

### 3. API Endpoints

**WebSocket-First Loading:**
- `GET /api/trade/initial/:poolAddress` - WebSocket cache first, API fallback
- `GET /api/trade/websocket/:poolAddress` - WebSocket cache only
- `GET /api/trade/websocket/status` - WebSocket service status

## Frontend Components

### 1. Enhanced Trades Table (`EnhancedTradesTable.tsx`)

**Features:**
- Full Mobula data integration
- Token symbol and name display
- Exchange information
- Real-time WebSocket updates
- Professional UI matching your screenshot

**Columns:**
- Age (time since trade)
- Token (symbol + name)
- Side (buy/sell with arrows)
- Price (formatted with scientific notation for small values)
- Amount (token amount with symbol)
- Total USD (formatted with K/M suffixes)
- Total SOL (SOL amount from Mobula)
- Exchange (DEX name)
- Transaction (link to Solscan)

### 2. Professional Trades Table (`ProfessionalTradesTable.tsx`)

**Features:**
- Exact replica of your screenshot
- Tab navigation
- Live data indicator
- Professional styling

### 3. Live Trades Table (`LiveTradesTable.tsx`)

**Features:**
- Same professional UI as screenshot
- Real WebSocket data integration
- Smart formatting for different value ranges

## Data Flow

```
Mobula WebSocket → mobulaTradeWebSocketService → tradeService → API → Frontend
                                ↓
                         Cache (100 trades/pool)
                                ↓
                         Real-time updates → Frontend WebSocket
```

## Key Improvements

### 1. Rich Data Display
- **Token Information**: Symbol, name, market cap
- **Exchange Details**: DEX name and type
- **Multiple Value Formats**: Token amount, SOL amount, USD value
- **Market Data**: 24h volume, price changes, liquidity

### 2. Smart Formatting
- **Scientific Notation**: For very small prices (< 0.000001)
- **K/M Suffixes**: For large amounts (1K, 1M, etc.)
- **Precision Control**: Different decimal places based on value size
- **Real-time Updates**: Live data with connection status

### 3. Professional UI
- **Tab Navigation**: Trades, Positions, Orders, etc.
- **Live Indicators**: Connection status and trade count
- **Hover Effects**: Smooth transitions and interactions
- **Responsive Design**: Works on different screen sizes

## Usage Examples

### Enhanced Table (Full Mobula Integration)
```tsx
<EnhancedTradesTable 
  poolAddress="CinCsUWhhcMavXwJpmsLCMNJDPoVFB4AKCor1gzsizJq"
  maxTrades={50}
  className="w-full"
/>
```

### Professional Table (Screenshot Match)
```tsx
<ProfessionalTradesTable 
  poolAddress="CinCsUWhhcMavXwJpmsLCMNJDPoVFB4AKCor1gzsizJq"
  maxTrades={50}
  className="w-full"
/>
```

## Configuration

### Environment Variables
```env
MOBULA_API_KEY=your_mobula_api_key_here
```

### WebSocket Connection
- **Primary URL**: `wss://api.mobula.io`
- **Fallback URL**: `wss://api-prod.mobula.io`
- **Heartbeat**: 30 seconds
- **Reconnection**: Exponential backoff (max 10 attempts)

## Testing

### API Endpoints
```bash
# Test WebSocket cache
curl "http://localhost:5001/api/trade/websocket/CinCsUWhhcMavXwJpmsLCMNJDPoVFB4AKCor1gzsizJq?limit=5"

# Test WebSocket status
curl "http://localhost:5001/api/trade/websocket/status"

# Test initial data (WebSocket first, API fallback)
curl "http://localhost:5001/api/trade/initial/CinCsUWhhcMavXwJpmsLCMNJDPoVFB4AKCor1gzsizJq?limit=5"
```

### Frontend Demo
Visit `/trades-demo` to see all table variations:
- **Enhanced**: Full Mobula integration
- **Professional**: Screenshot replica
- **Live**: Real-time updates
- **Compact**: Simplified view
- **Test**: WebSocket debugging

## Benefits

1. **Real-time Data**: Live trade updates via WebSocket
2. **Rich Metadata**: Token info, exchange details, market data
3. **Professional UI**: Matches your screenshot exactly
4. **Fallback Support**: API fallback when WebSocket unavailable
5. **Performance**: Cached data for fast initial loads
6. **Scalability**: Subscription management for multiple pools

The integration provides a complete solution for displaying real-time Solana trade data with professional UI and rich metadata from Mobula's comprehensive feed.
