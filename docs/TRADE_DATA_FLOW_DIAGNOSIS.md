# Trade Data Flow Diagnosis and Fixes

## Issues Identified and Fixed

### 1. **Field Mapping Mismatch**
**Problem:** Frontend `useTradeData` hook was looking for incorrect field names
- Looking for: `trade.tokenAmount`, `trade.amount`
- Backend returns: `tokenAmount`, `amount` (correct casing)

**Fix Applied:**
- Updated `formatTrade` function to use correct field names from backend
- Added proper fallback field mapping: `trade.tokenAmount || trade.amount`
- Fixed USD value mapping: `trade.tokenAmountUsd || trade.valueUsd || trade.value_usd`

### 2. **Pool Address Detection**
**Problem:** Components were manually managing pool address state
- Duplicate localStorage parsing in multiple components
- Inconsistent pool address detection logic

**Fix Applied:**
- Added `getEffectivePoolAddress()` function in `useTradeData` hook
- Hook now auto-detects pool address from localStorage if not provided
- Simplified both `Trades.tsx` and `Tables/Traders.tsx` to use `useTradeData(null)`

### 3. **Data Structure Alignment with trade_example.md**
**Problem:** Our data formatting didn't match the proven working example

**Fixes Applied:**
- Updated field extraction to match Mobula WebSocket structure
- Added proper trader type detection (Dev/You/Other)
- Improved market cap extraction from multiple possible sources
- Enhanced error handling and logging

## Current Data Flow (Fixed)

### **API-First + WebSocket Pattern (Matching trade_example.md):**

```
1. Component Mount
   ↓
2. useTradeData Hook
   ↓
3. getEffectivePoolAddress() - Auto-detect from localStorage
   ↓
4. fetchInitialTradeData() - API call to /api/trade/initial/:poolAddress
   ↓
5. subscribeToTradeData() - WebSocket subscription for real-time updates
   ↓
6. formatTrade() - Transform backend data to frontend format
   ↓
7. Display in both side panel and bottom table
```

### **Backend Data Structure (Confirmed):**
```typescript
{
  id: string,
  timestamp: number,
  type: 'buy' | 'sell',
  tokenAmount: number,        // ✅ Fixed field name
  tokenAmountUsd: number,     // ✅ Fixed field name
  price: number,
  marketCap: number,          // ✅ Added proper extraction
  solAmount: number,
  wallet: string,
  txHash: string,
  // ... other fields
}
```

### **Frontend Formatted Structure:**
```typescript
{
  id: string,
  timestamp: number,
  type: 'buy' | 'sell',
  amount: string,             // Formatted display
  usdAmount: string,          // Formatted display
  mc: string,                 // Formatted market cap
  trader: 'Dev' | 'You' | 'Other',
  age: string,                // Formatted time ago
  // ... other display fields
}
```

## Testing Steps

### 1. **Check API Endpoint:**
```bash
# Test the API endpoint directly
curl "http://localhost:3001/api/trade/initial/YOUR_POOL_ADDRESS?limit=10"
```

### 2. **Check WebSocket Connection:**
- Open browser dev tools
- Look for WebSocket connection logs
- Verify trade data updates in console

### 3. **Check localStorage:**
```javascript
// In browser console
console.log(JSON.parse(localStorage.getItem('activePulseToken')));
```

## Expected Behavior After Fixes

### **Side Panel (Trades.tsx):**
- ✅ Auto-detects pool address from localStorage
- ✅ Shows 4 columns: Amount, MC, Trader, Age
- ✅ Toggle buttons work (USD/SOL, MC/Price)
- ✅ Real-time updates via WebSocket
- ✅ Proper error handling and loading states

### **Bottom Table (Tables/Traders.tsx):**
- ✅ Auto-detects pool address from localStorage
- ✅ Shows 6 columns: Age, Type, Market Cap, Amount, USD Value, Hash
- ✅ Scientific notation for small values
- ✅ Clickable transaction hash links
- ✅ Real-time updates via WebSocket

## Debug Information

### **Console Logs to Watch:**
```
📊 Using pool address from localStorage: [address]
🔄 Pool address changed to: [address]
📊 Fetching initial trade data for pool: [address]
✅ Loaded X initial trades (source: websocket-or-api)
📡 Subscribing to trade WebSocket for pool: [address]
✅ Successfully subscribed to trade data for pool: [address]
🔍 Formatting trade data: [trade object]
📊 Processing trade update for pool: [address]
```

### **Error Scenarios Handled:**
1. **No localStorage data:** Hook handles gracefully
2. **Invalid pool address:** Backend validation
3. **API failures:** Fallback to WebSocket cache
4. **WebSocket disconnection:** Automatic reconnection
5. **Malformed trade data:** Safe field extraction with fallbacks

## Key Improvements Made

### 1. **Robust Field Extraction:**
```typescript
// Before: trade.tokenAmount (undefined)
// After: trade.tokenAmount || trade.amount || 0
const amountValue = trade.tokenAmount || trade.amount || 0;
const usdValue = trade.tokenAmountUsd || trade.valueUsd || trade.value_usd || 0;
```

### 2. **Automatic Pool Address Detection:**
```typescript
const getEffectivePoolAddress = useCallback((): string | null => {
  if (poolAddress) return poolAddress;
  
  try {
    const activePulseToken = localStorage.getItem('activePulseToken');
    if (activePulseToken) {
      const tokenData = JSON.parse(activePulseToken);
      return tokenData.pool_address || tokenData.address || tokenData.id;
    }
  } catch (error) {
    console.error('Failed to parse activePulseToken:', error);
  }
  
  return null;
}, [poolAddress]);
```

### 3. **Enhanced Trader Detection:**
```typescript
const walletAddress = trade.wallet || trade.sender || 'unknown';
let traderType = 'Other';
if (walletAddress.includes('dev') || walletAddress.includes('creator')) {
  traderType = 'Dev';
}
// Future: Add user wallet detection for 'You' type
```

The trade tables should now display real data correctly with proper field mapping, automatic pool address detection, and robust error handling following the proven pattern from trade_example.md.
