# RedFyn Spot Trading Platform - Production Deployment Guide

## Overview

This guide covers the complete deployment of the RedFyn Spot Trading Platform to production environment.

### Architecture
- **Frontend**: React application served via Nginx with SSL
- **Backend Services**: 3 Node.js microservices
- **Infrastructure**: Docker containers on Ubuntu server
- **Domain**: redfyn.crypfi.io
- **Server**: *************

## Services Configuration

### Service Ports
| Service | Development Port | Production Port | Description |
|---------|------------------|-----------------|-------------|
| Frontend | 4001 | 80/443 | React app with Nginx |
| Spot Backend | 5001 | 5001 | Main trading API |
| Liquidity Pool | 3047 | 3047 | Liquidity management |
| Solana Service | 6001 | 6001 | Solana blockchain integration |
| Redis | 6379 | 6379 | Caching service |

### Environment Files
Production environment files have been created:
- `spot_frontend/.env.production`
- `backend/spot_backend/.env.production`
- `backend/liquidity_pool/.env.production`
- `backend/solana/.env.production`

## Deployment Steps

### 1. Prerequisites
Ensure you have:
- SSH access to server *************
- Domain redfyn.crypfi.io pointing to the server
- Docker and Docker Compose installed on server

### 2. Automated Deployment
Run the deployment script:
```bash
./deploy-production.sh
```

This script will:
- Build all services locally
- Create deployment package
- Upload to production server
- Deploy using Docker Compose
- Start all services

### 3. Manual Deployment Steps

#### Step 1: Prepare Local Environment
```bash
# Install dependencies
npm install

# Build all services
cd spot_frontend && npm install --legacy-peer-deps && npm run build && cd ..
cd backend/spot_backend && npm install && npm run build && cd ../..
cd backend/liquidity_pool && npm install && npm run build && cd ../..
cd backend/solana && npm install && npm run build && cd ../..
```

#### Step 2: Deploy to Server
```bash
# Create deployment package
tar -czf redfyn-deployment.tar.gz \
  backend/ \
  spot_frontend/ \
  docker-compose.production.yml \
  nginx.conf \
  package.json

# Upload to server
scp redfyn-deployment.tar.gz root@*************:/tmp/

# Deploy on server
ssh root@*************
cd /tmp
tar -xzf redfyn-deployment.tar.gz
mv redfyn-deployment /opt/redfyn-spot
cd /opt/redfyn-spot

# Start services
docker-compose -f docker-compose.production.yml up --build -d
```

### 4. SSL Certificate Setup
```bash
# Run SSL setup script
./setup-ssl.sh
```

## Network Configuration

### DNS Records
Configure the following DNS records:
```
A    redfyn.crypfi.io    *************
A    www.redfyn.crypfi.io    *************
```

### Firewall Rules
Ensure the following ports are open:
```bash
# HTTP/HTTPS
ufw allow 80
ufw allow 443

# Backend services (if direct access needed)
ufw allow 5001
ufw allow 3047
ufw allow 6001

# SSH
ufw allow 22

# Redis (internal only)
ufw allow from *********/8 to any port 6379
```

## Service URLs

### Production URLs
- **Frontend**: https://redfyn.crypfi.io
- **Spot Backend API**: http://*************:5001
- **Liquidity Pool API**: http://*************:3047
- **Solana Service API**: http://*************:6001

### Health Check Endpoints
- Frontend: https://redfyn.crypfi.io/health
- Spot Backend: http://*************:5001/health
- Liquidity Pool: http://*************:3047/api/health
- Solana Service: http://*************:6001/health

## Monitoring and Maintenance

### Service Status
```bash
# Check all services
docker-compose -f docker-compose.production.yml ps

# View logs
docker-compose -f docker-compose.production.yml logs -f [service-name]

# Restart specific service
docker-compose -f docker-compose.production.yml restart [service-name]
```

### SSL Certificate Renewal
SSL certificates auto-renew via cron job:
```bash
0 12 * * * /usr/bin/certbot renew --quiet --post-hook 'cd /opt/redfyn-spot && docker-compose -f docker-compose.production.yml restart frontend'
```

### Backup Strategy
```bash
# Backup Redis data
docker exec redfyn-redis redis-cli BGSAVE

# Backup configuration
tar -czf backup-$(date +%Y%m%d).tar.gz /opt/redfyn-spot
```

## Troubleshooting

### Common Issues

1. **Services not starting**
   ```bash
   # Check logs
   docker-compose -f docker-compose.production.yml logs
   
   # Rebuild containers
   docker-compose -f docker-compose.production.yml up --build -d
   ```

2. **SSL certificate issues**
   ```bash
   # Check certificate status
   certbot certificates
   
   # Renew manually
   certbot renew --force-renewal
   ```

3. **Network connectivity issues**
   ```bash
   # Test internal connectivity
   docker exec redfyn-frontend curl http://redfyn-spot-backend:5001/health
   
   # Check port availability
   netstat -tulpn | grep :5001
   ```

### Performance Monitoring
- Monitor CPU and memory usage
- Check Redis memory usage
- Monitor API response times
- Track WebSocket connections

## Security Considerations

1. **Environment Variables**: Sensitive data stored in .env files
2. **SSL/TLS**: HTTPS enforced for frontend
3. **CORS**: Configured for production domains
4. **Firewall**: Restrictive rules for internal services
5. **Updates**: Regular security updates for base images

## Rollback Procedure

In case of deployment issues:
```bash
# Stop current deployment
docker-compose -f docker-compose.production.yml down

# Restore previous backup
mv /opt/redfyn-spot-backup-[timestamp] /opt/redfyn-spot

# Start previous version
cd /opt/redfyn-spot
docker-compose -f docker-compose.production.yml up -d
```
