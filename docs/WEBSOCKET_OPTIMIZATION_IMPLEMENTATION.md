# Real-Time Trading Data Implementation

## Overview

This implementation adds real-time trading data to the Redfyn-Spot platform by connecting to the Mobula WebSocket API. The implementation includes both backend and frontend components to provide a seamless experience for users.

## Backend Components

### TokenPairWebSocketService

- Connects to the Mobula WebSocket API
- Subscribes to specific token pairs using their addresses
- Maintains connections for active clients
- Caches trade data for each pair
- Calculates bonding curve data for tokens
- Handles reconnection, heartbeats, and graceful shutdown

### Trade Routes

- `/api/trade/token-pair/:address` - Get real-time trade data for a specific token pair
- `/api/trade/bonding-curve/:address` - Get bonding curve data for a specific token pair
- `/api/trade/status` - Get status of the WebSocket service
- Includes simulated data provider for testing

## Frontend Components

### Trades Component

- Fetches real-time trade data from the API
- Displays trades with proper formatting
- Supports toggling between USD and native currency
- Supports toggling between price and market cap
- Sorts trades by age
- Shows loading and error states
- Falls back to static data when needed

## Implementation Details

### WebSocket Connection

- Uses the Mobula WebSocket API at `wss://api.mobula.io`
- Authenticates using the Mobula API key
- Subscribes to token pairs using their addresses
- <PERSON><PERSON> reconnection with exponential backoff
- Maintains heartbeats to keep the connection alive

### Data Flow

1. Frontend requests trade data for a specific token pair
2. Backend checks if cached data is available
3. If no cached data, backend registers the pair with the WebSocket service
4. WebSocket service connects to Mobula API and subscribes to the pair
5. When data is received, it's cached and sent to the frontend
6. Frontend transforms the data and displays it to the user
7. Frontend polls for updates every 10 seconds

### Fallback Mechanism

- If the WebSocket connection fails, the backend provides simulated data
- If no data is available for a specific pair, the frontend falls back to static data
- The simulated data includes multiple trades with realistic values

## Configuration

- The Mobula API key is stored in the `.env` file
- The WebSocket URL is configurable in the `TokenPairWebSocketService`
- The polling interval is configurable in the frontend component

## Testing

- Use the `/api/trade/status` endpoint to check the WebSocket service status
- Use the `/api/trade/token-pair/:address` endpoint to test the trade data
- Use the `/api/trade/bonding-curve/:address` endpoint to test the bonding curve data

## Future Improvements

- Add support for multiple token pairs in a single request
- Implement WebSocket connection on the frontend to reduce polling
- Add more detailed error handling and logging
- Implement rate limiting to prevent abuse
- Add support for historical data
