can you check trading_panel service we need add one more service called hoders and top trades this is api service user can pass tokenMintaddress via pulse trade page,
api details is:
curl --request GET \
  --url 'https://api.mobula.io/api/1/market/token/holders?limit=20&blockchain=solana&asset=tokenAddress'
response
{
  "data": [
    {
      "address": "8t1Gy7p4uYkUBrG7UJt2zuNnQV5dpn4pR2aNhkjbfUXt",
      "amountRaw": "242225313186992",
      "amount": 242225313.186992,
      "chainId": "solana:solana",
      "totalSupplyShare": 24.2225313186992,
      "amountUSD": 10538.639804061695
    },
    {
      "address": "B5vjBY4vNcqnaLNgH488sD3nn9rbdCdFZdpWBdydyKtr",
      "amountRaw": "18878456838502",
      "amount": 18878456.838502,
      "chainId": "solana:solana",
      "totalSupplyShare": 1.8878456838502002,
      "amountUSD": 821.3561747937998
    },
]
}
top tades api detsila
curl --request GET \
  --url 'https://api.mobula.io/api/1/market/trades/pair?sortOrder=desc&mode=pair&blockchain=solana&asset=P3MN6m6esLkCG4MJAPFqJvc65iwnMzcttsenGqNpump&limit=100'
response
{
  "data": [
    {
      "blockchain": "Solana",
      "hash": "ix4PEvPUSQRjKyd1YMJX1hrHALnQduQrzyNuNn1ivPRPxDWQBm6xfAaH2jF1bWRVbZ8BGjTzjmf3kTAdHFpkYAw",
      "pair": "4yxGjbVBHudhxLGbvWbYn6WY5DqV1iy6zmMrxgfxHZn5",
      "date": 1750673050000,
      "token_price_vs": 134.3680814724592,
      "token_price": 0.00004357036303187414,
      "token_amount": 301987.066947,
      "token_amount_vs": 0.097922706,
      "token_amount_usd": 13.15768613781167,
      "type": "sell",
      "sender": "Co8GqWFw7EZmUhMeWWthodFnXJhr5JvZ9W5hdh2idNLQ",
      "transaction_sender_address": "Co8GqWFw7EZmUhMeWWthodFnXJhr5JvZ9W5hdh2idNLQ",
      "token_amount_raw": "301987066947",
      "token_amount_raw_vs": "97922706",
      "operation": "regular"
    },
    {
      "blockchain": "Solana",
      "hash": "3vC8jY6uSxB7wDnkq3Dgq4Z7fXFb6Hv9Gc6GUrfHS43kscWLVxhPxonVmpToA7shKQdpZnHTB9JghfYnphepJv2c",
      "pair": "4yxGjbVBHudhxLGbvWbYn6WY5DqV1iy6zmMrxgfxHZn5",
      "date": 1750672908000,
      "token_price_vs": 134.3837049276116,
      "token_price": 0.00004362354917733167,
      "token_amount": 45750.410755,
      "token_amount_vs": 0.014851468,
      "token_amount_usd": 1.995795293453866,
      "type": "sell",
      "sender": "D61iR2Ci51MGPmCHixw1NnDGJX4zMCXgCgFNtGLJLzwx",
      "transaction_sender_address": "D61iR2Ci51MGPmCHixw1NnDGJX4zMCXgCgFNtGLJLzwx",
      "token_amount_raw": "45750410755",
      "token_amount_raw_vs": "14851468",
      "operation": "regular"
    },
]
}
can you check pulse trade page bottom table have a holders and top trades can you make single api to get this data from mobula->trading_panel service ->frontend
top trade can you get 100 trade data to sort top 10 trades