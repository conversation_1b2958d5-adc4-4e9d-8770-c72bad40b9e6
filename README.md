# RedFyn - Multi-Chain DEX and Wallet Management Platform

A comprehensive cryptocurrency platform for traders and DeFi users with multi-chain support, advanced trading features, and rich wallet management capabilities.

![Trading Panel Screenshot](screenshot.png)

## Project Overview

RedFyn is a full-stack cryptocurrency platform consisting of:
1. **Frontend Trading Panel** - Modern React-based UI for trading and token management
2. **Spot Backend Service** - Core trading and wallet management API with WebSocket support
3. **Limit Orders Service** - Advanced limit order management and monitoring
4. **Trading Panel Service** - Real-time trading data aggregation and WebSocket services
5. **Solana Service** - Specialized Solana blockchain integration with MEV protection
6. **Memory Bank** - Project documentation and context store

## Architecture

RedFyn follows a microservices architecture with five main services:

### Frontend Service
- **Location**: `spot_frontend/`
- **Port**: 4001
- **Technology**: React + TypeScript + Vite + Privy + Socket.IO
- **Purpose**: User interface for trading, wallet management, and real-time data visualization

### Backend Services

#### 1. Spot Backend Service
- **Location**: `backend/spot_backend/`
- **Port**: 5001
- **Technology**: Node.js + Express + TypeScript + Socket.IO + Redis
- **Purpose**: Core trading API, market data aggregation, WebSocket management, Mobula integration

#### 2. Limit Orders Service
- **Location**: `backend/limit_orders/`
- **Port**: 5002
- **Technology**: Node.js + Express + TypeScript + Supabase
- **Purpose**: Advanced limit order creation, monitoring, and execution management

#### 3. Trading Panel Service
- **Location**: `backend/trading_panel/`
- **Port**: 5003
- **Technology**: Node.js + Express + TypeScript + WebSocket
- **Purpose**: Real-time trading data aggregation, volume analysis, and WebSocket services

#### 4. Solana Service
- **Location**: `backend/solana/`
- **Port**: 6000
- **Technology**: Node.js + Express + TypeScript + Anchor + Raydium SDK
- **Purpose**: Solana blockchain interactions, Pump.fun integration, MEV protection, Jito bundles

## Project Structure

```
redfyn-spot/
├── spot_frontend/            # React frontend application (Port: 4001)
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   │   ├── Trading/      # Trading panel components
│   │   │   ├── Wallet/       # Wallet connection components
│   │   │   └── UI/           # Generic UI elements
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Application pages/routes
│   │   ├── services/         # API services and external integrations
│   │   ├── store/            # State management (Redux/Context)
│   │   ├── utils/            # Helper functions and utilities
│   │   └── App.tsx           # Main application component
│   ├── public/               # Static assets
│   ├── .env                  # Environment variables
│   └── package.json          # Dependencies and scripts
├── backend/                  # Backend services directory
│   ├── spot_backend/         # Core trading backend (Port: 5001)
│   │   ├── src/
│   │   │   ├── controllers/  # API endpoint controllers
│   │   │   ├── routes/       # API route definitions (home, limit-orders, activity, trade-history)
│   │   │   ├── services/     # Business logic (pulse, websocket, mobula integration)
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── middleware/   # Express middleware
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point with health checks
│   │   ├── dist/             # Compiled TypeScript
│   │   ├── docs/             # Implementation documentation
│   │   ├── scripts/          # Utility and test scripts
│   │   └── tsconfig.json     # TypeScript configuration
│   ├── limit_orders/         # Limit orders service (Port: 5002)
│   │   ├── src/
│   │   │   ├── controllers/  # Limit order controllers
│   │   │   ├── routes/       # API routes for limit orders
│   │   │   ├── services/     # Order monitoring and execution services
│   │   │   ├── middleware/   # Authentication and validation
│   │   │   ├── utils/        # Helper functions
│   │   │   └── index.ts      # Application entry point
│   │   ├── dist/             # Compiled TypeScript
│   │   └── logs/             # Service logs
│   ├── trading_panel/        # Trading panel service (Port: 5003)
│   │   ├── src/
│   │   │   ├── controllers/  # Trading panel controllers
│   │   │   ├── routes/       # API routes for trading data
│   │   │   ├── services/     # WebSocket, volume aggregation, Mobula integration
│   │   │   ├── types/        # TypeScript type definitions
│   │   │   └── index.ts      # Application entry point with WebSocket server
│   │   ├── dist/             # Compiled TypeScript
│   │   └── README.md         # Service-specific documentation
│   └── solana/               # Solana blockchain service (Port: 6000)
│       ├── src/
│       │   ├── controllers/  # Jupiter, Pump.fun, MEV, token controllers
│       │   ├── services/     # Anchor, Jito, transaction, analysis services
│       │   ├── routes/       # Solana-specific API routes
│       │   ├── types/        # Solana type definitions
│       │   ├── utils/        # Solana utilities
│       │   └── index.ts      # Application entry point
│       ├── idl/              # Anchor IDL files
│       ├── docs/             # MEV and Jito implementation guides
│       └── test/             # Solana-specific tests
├── scripts/                  # Utility scripts
│   ├── start-services.sh     # Service startup script
│   └── check-port.sh         # Port checking utility
├── memory-bank/              # Project documentation and context
├── nginx/                    # Nginx configuration for production
├── docker-compose.yml        # Development Docker setup
├── docker-compose.prod.yml   # Production Docker setup
├── package.json              # Root package.json with unified scripts
└── README.md                 # Project documentation
```

## Core Features

### Frontend (Trading Panel)
- **Multi-Chain Support**: Trade across Ethereum, Solana, Polygon, Base, Arbitrum, Optimism, and more
- **Wallet Integration**: Seamless connection with multiple wallet types (Privy integration)
- **DEX Aggregation**: Finds the best prices across multiple DEXs
- **Real-Time Quotes**: Fetches best available prices with minimal latency (100ms debounce)
- **Advanced Trading Features**: Market & Limit orders, Take Profit/Stop Loss, Slippage control
- **Responsive Design**: Modern UI with intuitive controls

#### Frontend Architecture
- **Component-Based Structure**: Modular components for maintainability and reusability
- **Responsive Layout**: Mobile-first design with Tailwind CSS
- **State Management**: Context API with reducers for global state
- **API Integration**: Custom hooks for data fetching with caching and invalidation
- **Websocket Integration**: Real-time price updates and order book data
- **Modular Routing**: Dynamic routing with code splitting for improved performance

#### Key UI Components
- **Trading Panel**: The core component for executing trades
  - Token selector with search and balance display
  - Order type selector (Market/Limit/Stop)
  - Price input with real-time validation
  - Slippage tolerance configuration
  - Transaction confirmation modal
- **Wallet Dashboard**: Interface for viewing and managing assets
  - Multi-chain balance overview
  - Token list with sorting and filtering
  - Transaction history with status indicators
- **Charts**: Interactive price charts with multiple timeframe options
  - TradingView integration
  - Custom indicators and drawing tools
  - Volume display and market depth visualization

### Backend (Wallet & Token Services)
- **Multi-Chain Wallet Balance API**: 
  - Fetch balances across ETH, BSC, Solana networks
  - Automatic token discovery with comprehensive metadata
  - Transaction history analysis for complete token detection
- **Token Metadata Service**: 
  - Dynamic token metadata retrieval from multiple sources
  - Token image, name and symbol fetching with smart caching
  - Support for newly created tokens via on-chain data
- **DEX Integration**:
  - Price and liquidity data from multiple DEXs
  - Trading pair information and token swap capabilities
  - Real-time quote aggregation

### Real-Time Data & WebSocket Services
- **Multi-Service WebSocket Architecture**:
  - Spot Backend: Market data aggregation and pulse data streaming
  - Trading Panel: Real-time trading metrics and volume analysis
  - Frontend: Live price updates and order book data
  - Mobula Integration: Real-time price feeds with fallback mechanisms

#### WebSocket Implementation
- **Frontend WebSocket Client**: 
  - Health check monitoring with automatic reconnection
  - Room-based subscriptions (pulse, trading, market data)
  - Connection status tracking and error handling
  - Callback system for different data types
- **Backend WebSocket Servers**: 
  - Socket.IO implementation with CORS configuration
  - Client registration and room management
  - Integration with external data sources (Mobula, DexScreener)
  - User activity tracking and session management
- **Data Flow Architecture**: 
  - Primary: WebSocket cache for real-time data
  - Fallback: REST API calls when WebSocket data is stale
  - Caching: Redis integration for performance optimization
  - Error Handling: Comprehensive fallback mechanisms

#### Advanced Trading Features
- **Limit Orders System**:
  - Advanced order creation and monitoring
  - Supabase integration for order persistence
  - Real-time order status updates
  - Execution tracking and notifications
- **MEV Protection (Solana)**:
  - Jito bundle integration for MEV protection
  - Priority fee optimization
  - Transaction confirmation improvements
  - User-paid Jito implementation
- **Multi-Chain Support**:
  - Ethereum, BSC, Solana network integration
  - Universal data aggregation across networks
  - Network-specific highlights and trending tokens
  - Cross-chain balance and token discovery

#### Technology Stack
- **WebSocket**: Socket.IO for real-time communication
- **Database**: Supabase for limit orders and user data
- **Caching**: Redis for performance optimization
- **Blockchain**: Ethers.js, Solana Web3.js, Anchor framework
- **External APIs**: Mobula, DexScreener, Jupiter, Pump.fun

## Technology Stack

### Frontend
- **React** - UI framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Ethereum interaction
- **Privy** - Wallet connection
- **HeadlessUI** - UI components
- **Tailwind CSS** - Styling
- **React Query** - API data fetching and caching

### Backend
- **Node.js** - Runtime environment
- **Express** - Web framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Blockchain interaction
- **Axios** - HTTP client
- **Solana Web3.js** - Solana blockchain integration
- **Socket.IO** - Real-time WebSocket communication
- **Supabase** - Database for limit orders and user data
- **Redis** - Caching and session management
- **Anchor** - Solana program framework
- **Raydium SDK** - Solana DEX integration

## API Endpoints

### Spot Backend API (Port: 5001)
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/limit-orders/*` - Limit order management endpoints
- `GET /api/activity/*` - User activity tracking
- `GET /api/trade-history/*` - Trading history endpoints
- `GET /health` - Service health check
- `GET /websocket/health` - WebSocket service status

### Limit Orders API (Port: 5002)
- `POST /api/limit-orders/create` - Create new limit orders
- `GET /api/limit-orders/user/:userId` - Get user's limit orders
- `PUT /api/limit-orders/:orderId` - Update limit order
- `DELETE /api/limit-orders/:orderId` - Cancel limit order
- `GET /api/limit-orders/status/:orderId` - Check order status
- `GET /health` - Service health check

### Trading Panel API (Port: 5003)
- `GET /api/trading-panel/volume` - Volume aggregation data
- `GET /api/trading-panel/metrics` - Trading metrics and analytics
- `WebSocket /` - Real-time trading data streams

### Solana Service API (Port: 6000)
- `POST /api/jupiter/quote` - Jupiter DEX price quotes
- `POST /api/jupiter/swap` - Execute Jupiter swaps
- `POST /api/pumpfun/buy` - Pump.fun token purchases
- `POST /api/pumpfun/sell` - Pump.fun token sales
- `POST /api/mev/bundle` - MEV-protected transaction bundles
- `POST /api/token/metadata` - Solana token metadata
- `GET /api/test/connection` - Test Solana RPC connection

## Getting Started

### Quick Start (All Services)

Install dependencies and start all services with a single command:

```bash
# Install dependencies for all services
npm install

# Start all services concurrently
npm run dev
```

This will start:
- Frontend on http://localhost:4001
- Spot Backend on http://localhost:5001
- Limit Orders Service on http://localhost:5002
- Trading Panel Service on http://localhost:5003
- Solana Service on http://localhost:6000

### Individual Service Development

#### Frontend Development
```bash
cd spot_frontend
npm install
npm run dev
# Runs on http://localhost:4001
```

#### Spot Backend Development
```bash
cd backend/spot_backend
npm install
npm run dev
# Runs on http://localhost:5001
```

#### Limit Orders Service Development
```bash
cd backend/limit_orders
npm install
npm run dev
# Runs on http://localhost:5002
```

#### Trading Panel Service Development
```bash
cd backend/trading_panel
npm install
npm run dev
# Runs on http://localhost:5003
```

#### Solana Service Development
```bash
cd backend/solana
npm install
npm run dev
# Runs on http://localhost:6000
```

### Available Scripts

From the root directory:

```bash
# Development
npm run dev                    # Start all services concurrently
npm run dev:frontend          # Start only frontend
npm run dev:spot-backend      # Start only spot backend
npm run dev:limit-orders      # Start only limit orders service
npm run dev:trading-panel     # Start only trading panel service
npm run dev:solana            # Start only solana service

# Installation
npm run install:all           # Install dependencies for all services
npm run install:frontend      # Install frontend dependencies
npm run install:backends      # Install all backend dependencies

# Building
npm run build:all             # Build all services
npm run build:frontend        # Build frontend
npm run build:backends        # Build all backend services

# Testing
npm run test:all              # Run tests for all services
npm run test:frontend         # Run frontend tests
npm run test:backends         # Run backend tests

# Utilities
npm run clean                 # Clean all node_modules and dist folders
npm run ports                 # Check which ports are in use
```

### Troubleshooting

#### Common Issues

1. **Permission Denied Errors**
   ```bash
   # Fix node_modules binary permissions
   chmod -R +x */node_modules/.bin/*
   ```

2. **Port Already in Use**
   ```bash
   # Check which services are running
   npm run ports

   # Kill processes on specific ports
   sudo lsof -ti:4001 | xargs kill -9  # Frontend
   sudo lsof -ti:5001 | xargs kill -9  # Spot Backend
   sudo lsof -ti:5002 | xargs kill -9  # Limit Orders
   sudo lsof -ti:5003 | xargs kill -9  # Trading Panel
   sudo lsof -ti:6000 | xargs kill -9  # Solana Service
   ```

3. **Redis Connection Errors**
   ```bash
   # Install and start Redis (optional)
   sudo apt-get install redis-server
   sudo systemctl start redis-server
   ```

4. **Missing Dependencies**
   ```bash
   # Reinstall all dependencies
   npm run clean
   npm run install:all
   ```

#### Service Health Check

Use the built-in port checker to verify all services are running:
```bash
npm run ports
```

Expected output when all services are running:
```
✓ Frontend (Port 4001): RUNNING
✓ Spot Backend (Port 5001): RUNNING
✓ Limit Orders (Port 5002): RUNNING
✓ Trading Panel (Port 5003): RUNNING
✓ Solana Service (Port 6000): RUNNING
```

### Environment Setup

Each service requires its own environment configuration:

#### Spot Backend (.env in backend/spot_backend/)
```
PORT=5001
ALCHEMY_API_KEY_ETH=your_alchemy_key
BSCSCAN_API_KEY=your_bscscan_key
ETHERSCAN_API_KEY=your_etherscan_key
SOLANA_RPC_URL=your_solana_rpc
REDIS_URL=redis://localhost:6379
```

#### Limit Orders Service (.env in backend/limit_orders/)
```
PORT=5002
NODE_ENV=development
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
```

#### Trading Panel Service (.env in backend/trading_panel/)
```
PORT=5003
NODE_ENV=development
MOBULA_API_KEY=your_mobula_api_key
REDIS_URL=redis://localhost:6379
```

#### Solana Service (.env in backend/solana/)
```
PORT=6000
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com
```

#### Frontend (.env in spot_frontend/)
```
VITE_PRIVY_APP_ID=your_privy_app_id
VITE_API_URL=http://localhost:5001/api
VITE_LIMIT_ORDERS_API_URL=http://localhost:5002/api
VITE_TRADING_PANEL_API_URL=http://localhost:5003/api
VITE_SOLANA_API_URL=http://localhost:6000/api
```

## Security Features
- Real-time balance checking
- Insufficient funds detection
- High price impact warnings
- Transaction fee transparency
- Input validation and comprehensive error handling
- Multiple fallback mechanisms for API failures

## License

MIT